// Branding loading state
.branding-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-spinner {
    text-align: center;
    color: white;

    .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 16px;
    }

    p {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      opacity: 0.9;
    }
  }
}

// Branding error state (non-blocking)
.branding-error {
  position: fixed;
  top: 16px;
  right: 16px;
  background: rgba(255, 152, 0, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  backdrop-filter: blur(4px);
  
  small {
    margin: 0;
  }
}

// Main content transitions
.branding-loaded {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

// Spinner animation
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Existing styles
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  display: flex;
  flex: 1;
}

.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  transition: margin-left 0.3s ease;
}

.sidenav-collapsed {
  .content {
    margin-left: 64px; // Width of collapsed sidebar
  }
}
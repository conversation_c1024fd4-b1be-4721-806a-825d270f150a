import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil, finalize } from 'rxjs/operators';

import { BrandingService } from '../../core/services/branding.service';
import {
  BrandingConfig,
  BrandingUpdateRequest,
  COLOR_PALETTE,
  BRANDING_VALIDATION,
  DEFAULT_BRANDING,
} from '../../core/models/branding';

@Component({
  selector: 'app-branding-management',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatTabsModule,
    MatSlideToggleModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  template: `
    <div class="branding-management-container">
      <div class="header">
        <h1>App Branding Management</h1>
        <p>
          Customize your app's appearance and branding for different clients
        </p>
      </div>

      <div class="content" *ngIf="!isLoading; else loadingTemplate">
        <form [formGroup]="brandingForm" (ngSubmit)="onSubmit()">
          <mat-tab-group>
            <!-- Basic Information Tab -->
            <mat-tab label="Basic Information">
              <div class="tab-content">
                <mat-card>
                  <mat-card-header>
                    <mat-card-title>App Identity</mat-card-title>
                    <mat-card-subtitle
                      >Configure basic app information</mat-card-subtitle
                    >
                  </mat-card-header>
                  <mat-card-content>
                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Admin Key</mat-label>
                        <input
                          matInput
                          type="password"
                          formControlName="adminKey"
                          placeholder="Enter admin key"
                        />
                        <mat-icon matSuffix>key</mat-icon>
                        <mat-error *ngIf="isFieldInvalid('adminKey')">{{
                          getFieldError('adminKey')
                        }}</mat-error>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="half-width">
                        <mat-label>App Name</mat-label>
                        <input
                          matInput
                          formControlName="appName"
                          placeholder="e.g., Plumeria Staff"
                        />
                        <mat-error *ngIf="isFieldInvalid('appName')">{{
                          getFieldError('appName')
                        }}</mat-error>
                      </mat-form-field>

                      <mat-form-field appearance="outline" class="half-width">
                        <mat-label>Company Name</mat-label>
                        <input
                          matInput
                          formControlName="companyName"
                          placeholder="e.g., Plumeria Construction"
                        />
                        <mat-error *ngIf="isFieldInvalid('companyName')">{{
                          getFieldError('companyName')
                        }}</mat-error>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Tagline (Optional)</mat-label>
                        <input
                          matInput
                          formControlName="tagline"
                          placeholder="e.g., Construction Management Made Simple"
                        />
                        <mat-error *ngIf="isFieldInvalid('tagline')">{{
                          getFieldError('tagline')
                        }}</mat-error>
                      </mat-form-field>
                    </div>
                  </mat-card-content>
                </mat-card>
              </div>
            </mat-tab>

            <!-- Theme Colors Tab -->
            <mat-tab label="Theme Colors">
              <div class="tab-content">
                <mat-card>
                  <mat-card-header>
                    <mat-card-title>Color Scheme</mat-card-title>
                    <mat-card-subtitle
                      >Customize your app's color palette</mat-card-subtitle
                    >
                  </mat-card-header>
                  <mat-card-content>
                    <div class="color-section">
                      <h3>Primary Colors</h3>
                      <div class="color-row">
                        <mat-form-field
                          appearance="outline"
                          class="color-input"
                        >
                          <mat-label>Primary Color</mat-label>
                          <input
                            matInput
                            formControlName="primaryColor"
                            placeholder="#667eea"
                          />
                          <div
                            matSuffix
                            class="color-preview"
                            [style.background-color]="
                              brandingForm.get('primaryColor')?.value
                            "
                          ></div>
                          <mat-error *ngIf="isFieldInvalid('primaryColor')">{{
                            getFieldError('primaryColor')
                          }}</mat-error>
                        </mat-form-field>
                        <div class="color-palette">
                          <div
                            *ngFor="let color of colorPalette.primary"
                            class="color-option"
                            [style.background-color]="color"
                            (click)="selectColor('primaryColor', color)"
                            [class.selected]="
                              brandingForm.get('primaryColor')?.value === color
                            "
                          ></div>
                        </div>
                      </div>

                      <div class="color-row">
                        <mat-form-field
                          appearance="outline"
                          class="color-input"
                        >
                          <mat-label>Secondary Color</mat-label>
                          <input
                            matInput
                            formControlName="secondaryColor"
                            placeholder="#764ba2"
                          />
                          <div
                            matSuffix
                            class="color-preview"
                            [style.background-color]="
                              brandingForm.get('secondaryColor')?.value
                            "
                          ></div>
                          <mat-error *ngIf="isFieldInvalid('secondaryColor')">{{
                            getFieldError('secondaryColor')
                          }}</mat-error>
                        </mat-form-field>
                        <div class="color-palette">
                          <div
                            *ngFor="let color of colorPalette.secondary"
                            class="color-option"
                            [style.background-color]="color"
                            (click)="selectColor('secondaryColor', color)"
                            [class.selected]="
                              brandingForm.get('secondaryColor')?.value ===
                              color
                            "
                          ></div>
                        </div>
                      </div>
                    </div>

                    <div class="color-section">
                      <h3>Accent Colors</h3>
                      <div class="form-row">
                        <mat-form-field
                          appearance="outline"
                          class="third-width"
                        >
                          <mat-label>Accent Color</mat-label>
                          <input
                            matInput
                            formControlName="accentColor"
                            placeholder="#4CAF50"
                          />
                          <div
                            matSuffix
                            class="color-preview"
                            [style.background-color]="
                              brandingForm.get('accentColor')?.value
                            "
                          ></div>
                          <mat-error *ngIf="isFieldInvalid('accentColor')">{{
                            getFieldError('accentColor')
                          }}</mat-error>
                        </mat-form-field>

                        <mat-form-field
                          appearance="outline"
                          class="third-width"
                        >
                          <mat-label>Error Color</mat-label>
                          <input
                            matInput
                            formControlName="errorColor"
                            placeholder="#FF5722"
                          />
                          <div
                            matSuffix
                            class="color-preview"
                            [style.background-color]="
                              brandingForm.get('errorColor')?.value
                            "
                          ></div>
                          <mat-error *ngIf="isFieldInvalid('errorColor')">{{
                            getFieldError('errorColor')
                          }}</mat-error>
                        </mat-form-field>

                        <mat-form-field
                          appearance="outline"
                          class="third-width"
                        >
                          <mat-label>Background Color</mat-label>
                          <input
                            matInput
                            formControlName="backgroundColor"
                            placeholder="#F8FAFC"
                          />
                          <div
                            matSuffix
                            class="color-preview"
                            [style.background-color]="
                              brandingForm.get('backgroundColor')?.value
                            "
                          ></div>
                          <mat-error
                            *ngIf="isFieldInvalid('backgroundColor')"
                            >{{ getFieldError('backgroundColor') }}</mat-error
                          >
                        </mat-form-field>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>
              </div>
            </mat-tab>

            <!-- Custom Texts Tab -->
            <mat-tab label="Custom Texts">
              <div class="tab-content">
                <mat-card>
                  <mat-card-header>
                    <mat-card-title>App Text Content</mat-card-title>
                    <mat-card-subtitle
                      >Customize text content throughout the
                      app</mat-card-subtitle
                    >
                  </mat-card-header>
                  <mat-card-content>
                    <div class="text-section">
                      <h3>Login Screen</h3>
                      <div class="form-row">
                        <mat-form-field appearance="outline" class="half-width">
                          <mat-label>Welcome Text</mat-label>
                          <textarea
                            matInput
                            formControlName="loginWelcome"
                            placeholder="Welcome to\\nYour App"
                            rows="2"
                          ></textarea>
                          <mat-error *ngIf="isFieldInvalid('loginWelcome')">{{
                            getFieldError('loginWelcome')
                          }}</mat-error>
                        </mat-form-field>

                        <mat-form-field appearance="outline" class="half-width">
                          <mat-label>Subtitle</mat-label>
                          <input
                            matInput
                            formControlName="loginSubtitle"
                            placeholder="Your tagline here"
                          />
                          <mat-error *ngIf="isFieldInvalid('loginSubtitle')">{{
                            getFieldError('loginSubtitle')
                          }}</mat-error>
                        </mat-form-field>
                      </div>
                    </div>

                    <div class="text-section">
                      <h3>Dashboard</h3>
                      <div class="form-row">
                        <mat-form-field appearance="outline" class="half-width">
                          <mat-label>Welcome Text</mat-label>
                          <input
                            matInput
                            formControlName="dashboardWelcome"
                            placeholder="Welcome back,"
                          />
                          <mat-error
                            *ngIf="isFieldInvalid('dashboardWelcome')"
                            >{{ getFieldError('dashboardWelcome') }}</mat-error
                          >
                        </mat-form-field>

                        <mat-form-field appearance="outline" class="half-width">
                          <mat-label>Subtitle</mat-label>
                          <input
                            matInput
                            formControlName="dashboardSubtitle"
                            placeholder="Have a productive day!"
                          />
                          <mat-error
                            *ngIf="isFieldInvalid('dashboardSubtitle')"
                            >{{ getFieldError('dashboardSubtitle') }}</mat-error
                          >
                        </mat-form-field>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>
              </div>
            </mat-tab>

            <!-- Features Tab -->
            <mat-tab label="Features">
              <div class="tab-content">
                <mat-card>
                  <mat-card-header>
                    <mat-card-title>App Features</mat-card-title>
                    <mat-card-subtitle
                      >Enable or disable app features</mat-card-subtitle
                    >
                  </mat-card-header>
                  <mat-card-content>
                    <div class="feature-toggles">
                      <div class="toggle-item">
                        <mat-slide-toggle formControlName="showRegistration">
                          Show Registration Option
                        </mat-slide-toggle>
                        <p class="toggle-description">
                          Allow users to register new accounts
                        </p>
                      </div>

                      <div class="toggle-item">
                        <mat-slide-toggle formControlName="enableSocialLogin">
                          Enable Social Login
                        </mat-slide-toggle>
                        <p class="toggle-description">
                          Enable Google and Apple login options
                        </p>
                      </div>

                      <div class="toggle-item">
                        <mat-slide-toggle formControlName="enableBiometric">
                          Enable Biometric Authentication
                        </mat-slide-toggle>
                        <p class="toggle-description">
                          Allow fingerprint and face recognition login
                        </p>
                      </div>

                      <div class="toggle-item">
                        <mat-slide-toggle formControlName="showForgotPassword">
                          Show Forgot Password
                        </mat-slide-toggle>
                        <p class="toggle-description">
                          Display forgot password option on login screen
                        </p>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>
              </div>
            </mat-tab>
          </mat-tab-group>

          <!-- Action Buttons -->
          <div class="actions">
            <button
              mat-stroked-button
              type="button"
              (click)="resetToDefault()"
              [disabled]="isSaving"
            >
              <mat-icon>restore</mat-icon>
              Reset to Default
            </button>

            <button
              mat-raised-button
              color="primary"
              type="submit"
              [disabled]="brandingForm.invalid || isSaving"
            >
              <mat-icon *ngIf="!isSaving">save</mat-icon>
              <mat-spinner *ngIf="isSaving" diameter="20"></mat-spinner>
              {{ isSaving ? 'Updating...' : 'Update Branding' }}
            </button>
          </div>
        </form>
      </div>

      <!-- Loading Template -->
      <ng-template #loadingTemplate>
        <div class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
          <p>Loading branding configuration...</p>
        </div>
      </ng-template>
    </div>
  `,
  styleUrls: ['./branding-management.component.scss'],
})
export class BrandingManagementComponent implements OnInit, OnDestroy {
  brandingForm!: FormGroup;
  currentBranding: BrandingConfig | null = null;
  isLoading = false;
  isSaving = false;
  colorPalette = COLOR_PALETTE;
  validation = BRANDING_VALIDATION;

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private brandingService: BrandingService,
    private dialog: MatDialog
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadCurrentBranding();
    this.subscribeToFormChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.brandingForm = this.fb.group({
      // Admin Key
      adminKey: ['', [Validators.required]],

      // Basic Information
      appName: [
        DEFAULT_BRANDING.appName,
        [
          Validators.required,
          Validators.minLength(this.validation.appName.minLength),
          Validators.maxLength(this.validation.appName.maxLength),
        ],
      ],
      companyName: [
        DEFAULT_BRANDING.companyName,
        [
          Validators.required,
          Validators.minLength(this.validation.companyName.minLength),
          Validators.maxLength(this.validation.companyName.maxLength),
        ],
      ],
      tagline: [
        DEFAULT_BRANDING.tagline,
        [Validators.maxLength(this.validation.tagline.maxLength)],
      ],

      // Theme Colors
      primaryColor: [
        DEFAULT_BRANDING.primaryColor,
        [
          Validators.required,
          Validators.pattern(this.validation.colors.pattern),
        ],
      ],
      secondaryColor: [
        DEFAULT_BRANDING.secondaryColor,
        [
          Validators.required,
          Validators.pattern(this.validation.colors.pattern),
        ],
      ],
      accentColor: [
        DEFAULT_BRANDING.accentColor,
        [
          Validators.required,
          Validators.pattern(this.validation.colors.pattern),
        ],
      ],
      errorColor: [
        DEFAULT_BRANDING.errorColor,
        [
          Validators.required,
          Validators.pattern(this.validation.colors.pattern),
        ],
      ],
      backgroundColor: [
        DEFAULT_BRANDING.backgroundColor,
        [
          Validators.required,
          Validators.pattern(this.validation.colors.pattern),
        ],
      ],

      // Custom Texts
      loginWelcome: [
        DEFAULT_BRANDING.loginWelcome,
        [Validators.maxLength(this.validation.texts.loginWelcome.maxLength)],
      ],
      loginSubtitle: [
        DEFAULT_BRANDING.loginSubtitle,
        [Validators.maxLength(this.validation.texts.loginSubtitle.maxLength)],
      ],
      dashboardWelcome: [
        DEFAULT_BRANDING.dashboardWelcome,
        [
          Validators.maxLength(
            this.validation.texts.dashboardWelcome.maxLength
          ),
        ],
      ],
      dashboardSubtitle: [
        DEFAULT_BRANDING.dashboardSubtitle,
        [
          Validators.maxLength(
            this.validation.texts.dashboardSubtitle.maxLength
          ),
        ],
      ],

      // Feature Toggles
      showRegistration: [DEFAULT_BRANDING.showRegistration],
      enableSocialLogin: [DEFAULT_BRANDING.enableSocialLogin],
      enableBiometric: [DEFAULT_BRANDING.enableBiometric],
      showForgotPassword: [DEFAULT_BRANDING.showForgotPassword],

      // Assets (URLs)
      logoUrl: [''],
      appIconUrl: [''],
      splashImageUrl: [''],
    });
  }

  private loadCurrentBranding(): void {
    this.isLoading = true;
    this.brandingService
      .getCurrentBranding()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => (this.isLoading = false))
      )
      .subscribe({
        next: (branding) => {
          this.currentBranding = branding;
          this.populateForm(branding);
        },
        error: (error) => {
          console.error('Error loading branding:', error);
          alert('Failed to load current branding configuration');
        },
      });
  }

  private populateForm(branding: BrandingConfig): void {
    this.brandingForm.patchValue({
      appName: branding.appName,
      companyName: branding.companyName,
      tagline: branding.tagline || '',
      primaryColor: branding.primaryColor,
      secondaryColor: branding.secondaryColor,
      accentColor: branding.accentColor,
      errorColor: branding.errorColor,
      backgroundColor: branding.backgroundColor,
      loginWelcome: branding.loginWelcome || '',
      loginSubtitle: branding.loginSubtitle || '',
      dashboardWelcome: branding.dashboardWelcome || '',
      dashboardSubtitle: branding.dashboardSubtitle || '',
      showRegistration: branding.showRegistration,
      enableSocialLogin: branding.enableSocialLogin,
      enableBiometric: branding.enableBiometric,
      showForgotPassword: branding.showForgotPassword,
      logoUrl: branding.logoUrl || '',
      appIconUrl: branding.appIconUrl || '',
      splashImageUrl: branding.splashImageUrl || '',
    });
  }

  private subscribeToFormChanges(): void {
    // Subscribe to color changes for live preview
    [
      'primaryColor',
      'secondaryColor',
      'accentColor',
      'errorColor',
      'backgroundColor',
    ].forEach((colorField) => {
      this.brandingForm
        .get(colorField)
        ?.valueChanges.pipe(takeUntil(this.destroy$))
        .subscribe((value) => {
          if (this.brandingService.isValidColor(value)) {
            this.updatePreview();
          }
        });
    });
  }

  selectColor(colorType: string, color: string): void {
    this.brandingForm.patchValue({ [colorType]: color });
    this.updatePreview();
  }

  updatePreview(): void {
    if (this.brandingForm.valid) {
      const formValue = this.brandingForm.value;
      const previewBranding: BrandingConfig = {
        ...this.currentBranding,
        ...formValue,
        id: this.currentBranding?.id,
        version: (this.currentBranding?.version || 0) + 1,
        isActive: true,
      } as BrandingConfig;

      this.brandingService.applyBrandingToPage(previewBranding);
    }
  }

  onSubmit(): void {
    if (this.brandingForm.valid) {
      this.saveBranding();
    } else {
      this.markFormGroupTouched();
      alert('Please fix the form errors before submitting');
    }
  }

  private saveBranding(): void {
    const formValue = this.brandingForm.value;
    const adminKey = formValue.adminKey;

    const brandingUpdate: BrandingUpdateRequest = {
      appName: formValue.appName,
      companyName: formValue.companyName,
      tagline: formValue.tagline || undefined,
      theme: {
        primaryColor: formValue.primaryColor,
        secondaryColor: formValue.secondaryColor,
        accentColor: formValue.accentColor,
        errorColor: formValue.errorColor,
        backgroundColor: formValue.backgroundColor,
      },
      texts: {
        loginWelcome: formValue.loginWelcome || undefined,
        loginSubtitle: formValue.loginSubtitle || undefined,
        dashboardWelcome: formValue.dashboardWelcome || undefined,
        dashboardSubtitle: formValue.dashboardSubtitle || undefined,
      },
      assets: {
        logoUrl: formValue.logoUrl || undefined,
        appIconUrl: formValue.appIconUrl || undefined,
        splashImageUrl: formValue.splashImageUrl || undefined,
      },
      features: {
        showRegistration: formValue.showRegistration,
        enableSocialLogin: formValue.enableSocialLogin,
        enableBiometric: formValue.enableBiometric,
        showForgotPassword: formValue.showForgotPassword,
      },
    };

    this.isSaving = true;
    this.brandingService
      .updateBranding(brandingUpdate, adminKey)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => (this.isSaving = false))
      )
      .subscribe({
        next: (updatedBranding) => {
          this.currentBranding = updatedBranding;
          alert('Branding updated successfully!');
          // Clear admin key for security
          this.brandingForm.patchValue({ adminKey: '' });
        },
        error: (error) => {
          console.error('Error updating branding:', error);
          alert(error.message || 'Failed to update branding configuration');
        },
      });
  }

  resetToDefault(): void {
    const adminKey = this.brandingForm.get('adminKey')?.value;
    if (!adminKey) {
      alert('Please enter admin key to reset branding');
      return;
    }

    this.isSaving = true;
    this.brandingService
      .resetToDefault(adminKey)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => (this.isSaving = false))
      )
      .subscribe({
        next: (defaultBranding) => {
          this.currentBranding = defaultBranding;
          this.populateForm(defaultBranding);
          alert('Branding reset to default successfully!');
        },
        error: (error) => {
          console.error('Error resetting branding:', error);
          alert(error.message || 'Failed to reset branding');
        },
      });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.brandingForm.controls).forEach((key) => {
      const control = this.brandingForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const control = this.brandingForm.get(fieldName);
    if (control?.errors && control.touched) {
      if (control.errors['required']) return `${fieldName} is required`;
      if (control.errors['minlength']) return `${fieldName} is too short`;
      if (control.errors['maxlength']) return `${fieldName} is too long`;
      if (control.errors['pattern']) return `Invalid ${fieldName} format`;
    }
    return '';
  }

  isFieldInvalid(fieldName: string): boolean {
    const control = this.brandingForm.get(fieldName);
    return !!(control?.invalid && control.touched);
  }
}

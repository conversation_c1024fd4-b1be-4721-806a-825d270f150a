.branding-management-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .header {
    margin-bottom: 32px;
    text-align: center;

    h1 {
      font-size: 2.5rem;
      font-weight: 300;
      margin-bottom: 8px;
      color: #333;
    }

    p {
      font-size: 1.1rem;
      color: #666;
      margin: 0;
    }
  }

  .content {
    .tab-content {
      padding: 24px 0;
    }

    mat-card {
      margin-bottom: 24px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      mat-card-header {
        margin-bottom: 24px;

        mat-card-title {
          font-size: 1.5rem;
          font-weight: 500;
          color: #333;
        }

        mat-card-subtitle {
          font-size: 1rem;
          color: #666;
          margin-top: 4px;
        }
      }
    }

    // Form Layout
    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .full-width {
      width: 100%;
    }

    .half-width {
      flex: 1;
    }

    .third-width {
      flex: 1;
    }

    // Color Section Styling
    .color-section {
      margin-bottom: 32px;

      h3 {
        font-size: 1.2rem;
        font-weight: 500;
        margin-bottom: 16px;
        color: #333;
      }

      .color-row {
        display: flex;
        align-items: flex-start;
        gap: 24px;
        margin-bottom: 24px;

        .color-input {
          min-width: 200px;

          .color-preview {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: 2px solid #ddd;
            margin-left: 8px;
            cursor: pointer;
            transition: transform 0.2s ease;

            &:hover {
              transform: scale(1.1);
            }
          }
        }

        .color-palette {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          max-width: 400px;

          .color-option {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.2s ease;
            position: relative;

            &:hover {
              transform: scale(1.1);
              border-color: #333;
            }

            &.selected {
              border-color: #333;
              transform: scale(1.15);

              &::after {
                content: '✓';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                font-size: 14px;
              }
            }
          }
        }
      }
    }

    // Text Section Styling
    .text-section {
      margin-bottom: 32px;

      h3 {
        font-size: 1.2rem;
        font-weight: 500;
        margin-bottom: 16px;
        color: #333;
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 8px;
      }
    }

    // Feature Toggles Styling
    .feature-toggles {
      .toggle-item {
        margin-bottom: 24px;
        padding: 16px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background-color: #fafafa;

        mat-slide-toggle {
          margin-bottom: 8px;
        }

        .toggle-description {
          margin: 0;
          font-size: 0.9rem;
          color: #666;
          line-height: 1.4;
        }
      }
    }

    // Action Buttons
    .actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 32px;
      padding: 24px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border-top: 3px solid var(--primary-color, #667eea);

      button {
        min-width: 160px;
        height: 48px;
        font-weight: 500;
        border-radius: 6px;

        mat-icon {
          margin-right: 8px;
        }

        &[mat-stroked-button] {
          border-color: #ddd;
          color: #666;

          &:hover {
            background-color: #f5f5f5;
            border-color: #999;
          }
        }

        &[mat-raised-button] {
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }
  }

  // Loading State
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;

    mat-spinner {
      margin-bottom: 24px;
    }

    p {
      font-size: 1.1rem;
      color: #666;
      margin: 0;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .branding-management-container {
    padding: 16px;

    .header {
      h1 {
        font-size: 2rem;
      }
    }

    .content {
      .form-row {
        flex-direction: column;
        gap: 16px;

        .half-width,
        .third-width {
          width: 100%;
        }
      }

      .color-section {
        .color-row {
          flex-direction: column;
          gap: 16px;

          .color-input {
            min-width: auto;
            width: 100%;
          }

          .color-palette {
            max-width: 100%;
          }
        }
      }

      .actions {
        flex-direction: column;
        gap: 16px;

        button {
          width: 100%;
        }
      }
    }
  }
}

// Custom Snackbar Styles
::ng-deep {
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }

  .warning-snackbar {
    background-color: #ff9800 !important;
    color: white !important;
  }
}

// Material Form Field Customization
::ng-deep {
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      .mat-mdc-form-field-flex {
        .mat-mdc-form-field-infix {
          min-height: 56px;
        }
      }
    }

    &.color-input {
      .mat-mdc-form-field-infix {
        display: flex;
        align-items: center;
      }
    }
  }

  .mat-mdc-tab-group {
    .mat-mdc-tab-header {
      border-bottom: 1px solid #e0e0e0;
    }

    .mat-mdc-tab-body-wrapper {
      .mat-mdc-tab-body {
        .mat-mdc-tab-body-content {
          overflow: visible;
        }
      }
    }
  }
}

// Animation for color preview
@keyframes colorChange {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.color-preview {
  animation: colorChange 0.3s ease-in-out;
}

.staff-list-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;

  h1 {
    font-size: 28px;
    font-weight: 500;
    color: #333;
    margin: 0;
  }

  button {
    min-width: 140px;
    height: 44px;
    border-radius: 6px;
    font-weight: 500;
    text-transform: none;

    mat-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }
}

mat-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;

  mat-card-content {
    padding: 24px;
  }
}

// Filter section with professional layout
.filter-container {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: flex-end;
  flex-wrap: wrap;

  .search-field {
    flex: 2;
    min-width: 300px;

    mat-form-field {
      width: 100%;
    }
  }

  .filter-field {
    flex: 1;
    min-width: 200px;

    mat-form-field {
      width: 100%;
    }
  }

  .filter-actions {
    display: flex;
    align-items: center;

    button {
      height: 40px;
      min-width: 100px;
      border-radius: 6px;
      font-weight: 500;
      text-transform: none;

      mat-icon {
        margin-right: 6px;
        font-size: 16px;
      }
    }
  }
}

// Action buttons section
.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;

  .left-actions {
    display: flex;
    align-items: center;
    gap: 16px;

    button {
      height: 36px;
      border-radius: 6px;
      font-weight: 500;
      text-transform: none;

      mat-icon {
        margin-right: 6px;
        font-size: 16px;
      }
    }

    .show-inactive-toggle {
      margin-left: 8px;

      .mat-mdc-slide-toggle-label {
        font-weight: 500;
        color: #555;
      }
    }
  }

  .right-actions {
    .total-count {
      color: #666;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

// Loading, error, and no-data states
.loading-container,
.error-container,
.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
    color: #999;
  }

  p {
    color: #666;
    font-size: 16px;
    margin: 0 0 16px 0;
  }

  button {
    margin-top: 8px;
  }
}

.error-container {
  mat-icon {
    color: #f44336;
  }

  p {
    color: #f44336;
  }
}

// Table styling
.table-container {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;

  table {
    width: 100%;

    th {
      background-color: #f8f9fa;
      color: #333;
      font-weight: 600;
      padding: 16px;
      border-bottom: 2px solid #e0e0e0;
    }

    td {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      vertical-align: middle;
    }

    tr:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }
}

// Profile picture styling
.profile-picture {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 2px solid #e0e0e0;
  flex-shrink: 0; // Prevent shrinking
  position: relative;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block; // Prevent inline spacing issues
    transition: opacity 0.2s ease; // Smooth loading

    &:not([src]),
    &[src=""] {
      opacity: 0;
    }
  }

  // Icon styling for when no image is available
  .profile-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  // Show icon when image fails or is not available
  &.show-icon {
    .profile-icon {
      display: flex;
    }

    img {
      display: none;
    }
  }
}

// Staff name cell styling
.staff-name-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .name {
    font-weight: 500;
    color: #333;
    font-size: 14px;
  }

  .gender {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    font-weight: 500;
  }
}

// Link styling
.email-link,
.phone-link {
  color: #3f51b5;
  text-decoration: none;
  font-size: 14px;

  &:hover {
    text-decoration: underline;
  }
}

// Badge styling
.department-badge,
.designation-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.designation-badge {
  background-color: #f3e5f5;
  color: #7b1fa2;
  border-color: #ce93d8;
}

// Status badge styling
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;

  &.active {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
  }

  &.inactive {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
  }
}

// Action buttons
.mat-mdc-icon-button {
  margin: 0 2px;
  width: 36px;
  height: 36px;

  mat-icon {
    font-size: 18px;
  }
}

// Paginator styling
mat-paginator {
  border-top: 1px solid #e0e0e0;
  background-color: #fafafa;
}

// Responsive design
@media (max-width: 1024px) {
  .staff-list-container {
    padding: 16px;
  }

  .filter-container {
    gap: 12px;

    .search-field,
    .filter-field {
      min-width: 250px;
    }
  }
}

@media (max-width: 768px) {
  .staff-list-container {
    padding: 12px;
  }

  .list-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;

    button {
      width: 100%;
    }
  }

  .filter-container {
    flex-direction: column;
    gap: 12px;

    .search-field,
    .filter-field {
      flex: 1 1 100%;
      min-width: auto;
    }

    .filter-actions {
      justify-content: center;
    }
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;

    .left-actions {
      justify-content: center;
      flex-wrap: wrap;
    }

    .right-actions {
      text-align: center;
    }
  }

  .table-container {
    overflow-x: auto;

    table {
      min-width: 800px;

      th,
      td {
        padding: 8px 12px;
        font-size: 13px;
      }
    }
  }
}

@media (max-width: 480px) {
  .list-header h1 {
    font-size: 24px;
  }

  .action-buttons {
    padding: 12px;
  }

  .table-container table {
    th,
    td {
      padding: 6px 8px;
      font-size: 12px;
    }
  }
}

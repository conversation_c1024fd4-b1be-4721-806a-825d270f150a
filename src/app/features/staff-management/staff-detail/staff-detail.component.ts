import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

import { StaffService } from '../../../core/services/staff.service';
import { Staff } from '../../../core/models/staff';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-staff-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTabsModule,
    MatDividerModule,
    MatChipsModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  templateUrl: './staff-detail.component.html',
  styleUrls: ['./staff-detail.component.scss'],
})
export class StaffDetailComponent implements OnInit {
  staff: Staff | null = null;
  isLoading = true;
  errorMessage = '';
  staffId: number | null = null;
  profileImageError = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private staffService: StaffService
  ) {}

  ngOnInit(): void {
    this.loadStaffData();
  }

  loadStaffData(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (!id) {
      this.router.navigate(['/admin/staff-management']);
      return;
    }

    this.staffId = parseInt(id, 10);
    this.isLoading = true;
    this.errorMessage = '';
    this.profileImageError = false;

    console.log('=== LOADING STAFF DETAIL ===');
    console.log('Staff ID:', this.staffId);

    this.staffService.getStaffById(this.staffId).subscribe({
      next: (response) => {
        console.log('=== STAFF DETAIL RESPONSE ===');
        console.log('Raw response:', response);

        if (response.success) {
          this.staff = response.data;
          console.log('Staff loaded:', this.staff);

          // Debug profile picture
          if (this.staff?.profile_picture) {
            console.log('Profile picture data:', {
              original: this.staff.profile_picture,
              url: this.getProfilePictureUrl(),
            });

            // Test profile picture URL in development
            if (!environment.production) {
              this.testProfilePictureUrl();
            }
          } else {
            console.log('No profile picture found for staff');
          }
        } else {
          this.errorMessage = response.message || 'Failed to load staff data';
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('=== STAFF DETAIL ERROR ===');
        console.error('Error:', error);
        this.errorMessage =
          'Error loading staff data: ' + this.getErrorMessage(error);
        this.isLoading = false;
      },
    });
  }

  onEdit(): void {
    if (this.staffId) {
      this.router.navigate(['/admin/staff-management/edit', this.staffId]);
    }
  }

  onDelete(): void {
    if (!this.staff) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Staff Member',
        message: `Are you sure you want to delete "${this.staff.staff_name}"? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && this.staffId) {
        this.staffService.deleteStaff(this.staffId).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('Staff member deleted successfully', 'Close', {
                duration: 3000,
              });
              this.router.navigate(['/admin/staff-management']);
            } else {
              this.snackBar.open(
                response.message || 'Failed to delete staff member',
                'Close',
                {
                  duration: 3000,
                }
              );
            }
          },
          error: (error) => {
            this.snackBar.open(
              'Error deleting staff member: ' + this.getErrorMessage(error),
              'Close',
              {
                duration: 3000,
              }
            );
          },
        });
      }
    });
  }

  onToggleStatus(): void {
    if (!this.staff || !this.staffId) return;

    const newStatus = !this.staff.is_active;
    this.staffService.toggleStaffStatus(this.staffId, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          this.staff!.is_active = newStatus;
          this.snackBar.open(
            `Staff ${newStatus ? 'activated' : 'deactivated'} successfully`,
            'Close',
            { duration: 3000 }
          );
        } else {
          this.snackBar.open(
            response.message || 'Failed to update status',
            'Close',
            {
              duration: 3000,
            }
          );
        }
      },
      error: (error) => {
        this.snackBar.open(
          'Error updating status: ' + this.getErrorMessage(error),
          'Close',
          {
            duration: 3000,
          }
        );
      },
    });
  }

  onBackToList(): void {
    this.router.navigate(['/admin/staff-management']);
  }

  // Enhanced profile picture methods
  getProfilePictureUrl(): string {
    if (!this.staff?.profile_picture) {
      return '';
    }

    const url = this.staffService.getProfilePictureUrl(
      this.staff.profile_picture
    );
    console.log(`Profile URL for ${this.staff.staff_name}:`, {
      original: this.staff.profile_picture,
      generated: url,
    });
    return url;
  }

  hasProfilePicture(): boolean {
    const hasUrl = !!(
      this.staff?.profile_picture &&
      this.staff.profile_picture.trim() &&
      !this.profileImageError
    );
    console.log(
      `Has profile picture for ${this.staff?.staff_name}:`,
      hasUrl,
      this.staff?.profile_picture
    );
    return hasUrl;
  }

  // Enhanced image error handling
  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    console.log('❌ Profile image load error:', {
      src: img.src,
      alt: img.alt,
      naturalWidth: img.naturalWidth,
      naturalHeight: img.naturalHeight,
      staffName: this.staff?.staff_name,
    });

    this.profileImageError = true;

    // Hide the image
    img.style.display = 'none';

    // Show fallback in the container
    const container = img.closest('.profile-picture');
    if (container) {
      container.classList.add('show-fallback');
    }
  }

  // Image load success handler
  onImageLoad(event: Event): void {
    const img = event.target as HTMLImageElement;
    console.log('✅ Profile image loaded successfully:', {
      src: img.src,
      alt: img.alt,
      naturalWidth: img.naturalWidth,
      naturalHeight: img.naturalHeight,
      staffName: this.staff?.staff_name,
    });

    this.profileImageError = false;

    // Ensure fallback is hidden
    const container = img.closest('.profile-picture');
    if (container) {
      container.classList.remove('show-fallback');
    }
  }

  // Test profile picture URL
  private testProfilePictureUrl(): void {
    if (!this.staff?.profile_picture) {
      console.log(`No profile picture for ${this.staff?.staff_name}`);
      return;
    }

    const url = this.getProfilePictureUrl();
    console.log(`Testing profile URL for ${this.staff.staff_name}:`, url);

    // Create a test image to check if URL is accessible
    const testImg = new Image();
    testImg.onload = () => {
      console.log(
        `✅ Profile image accessible for ${this.staff?.staff_name}:`,
        {
          url,
          width: testImg.naturalWidth,
          height: testImg.naturalHeight,
        }
      );
    };
    testImg.onerror = (error) => {
      console.log(
        `❌ Profile image not accessible for ${this.staff?.staff_name}:`,
        {
          url,
          error,
        }
      );
    };
    testImg.src = url;
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'Not specified';
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  formatCurrency(amount: number | null | undefined): string {
    if (!amount) return 'Not specified';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount);
  }

  getAge(): string {
    if (!this.staff?.date_of_birth) return 'Not specified';

    const birthDate = new Date(this.staff.date_of_birth);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return `${age} years`;
  }

  getExperience(): string {
    if (!this.staff?.joining_date) return 'Not specified';

    const joiningDate = new Date(this.staff.joining_date);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - joiningDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);

    if (years > 0) {
      return `${years} year${years > 1 ? 's' : ''} ${months} month${
        months > 1 ? 's' : ''
      }`;
    } else {
      return `${months} month${months > 1 ? 's' : ''}`;
    }
  }

  private getErrorMessage(error: any): string {
    if (error.error?.message) {
      return error.error.message;
    }
    if (error.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  }

  // Helper methods for displaying data
  getDisplayValue(value: any): string {
    return value || 'Not specified';
  }

  getMaskedValue(value: string, maskLength: number = 4): string {
    if (!value) return 'Not specified';
    if (value.length <= maskLength) return value;

    const visiblePart = value.slice(-maskLength);
    const maskedPart = '*'.repeat(value.length - maskLength);
    return maskedPart + visiblePart;
  }

  // Contact methods
  callMobile(): void {
    if (this.staff?.staff_mobile) {
      window.open(`tel:${this.staff.staff_mobile}`, '_self');
    }
  }

  sendEmail(): void {
    if (this.staff?.staff_email) {
      window.open(`mailto:${this.staff.staff_email}`, '_self');
    }
  }

  callEmergencyContact(): void {
    if (this.staff?.emergency_contact_phone) {
      window.open(`tel:${this.staff.emergency_contact_phone}`, '_self');
    }
  }
}

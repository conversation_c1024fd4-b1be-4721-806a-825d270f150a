.staff-detail-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #f8f9fa;
  min-height: 100vh;
}

// Header Section
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 20px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e0e0e0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    button {
      width: 44px;
      height: 44px;
      border-radius: 8px;
      background-color: #f5f5f5;
      border: 1px solid #e0e0e0;
      transition: all 0.2s ease;

      &:hover {
        background-color: #e8f4fd;
        border-color: #2196f3;
        
        mat-icon {
          color: #2196f3;
        }
      }

      mat-icon {
        color: #666;
        transition: color 0.2s ease;
      }
    }

    h1 {
      font-size: 28px;
      font-weight: 600;
      color: #333;
      margin: 0;
      letter-spacing: -0.5px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;

    button {
      height: 44px;
      border-radius: 8px;
      font-weight: 500;
      text-transform: none;
      min-width: 120px;
      font-size: 14px;
      border: 1px solid;
      transition: all 0.2s ease;

      mat-icon {
        margin-right: 8px;
        font-size: 18px;
      }

      &[color="warn"] {
        background-color: #fff5f5;
        border-color: #f56565;
        color: #c53030;

        &:hover {
          background-color: #fed7d7;
        }
      }

      &[color="primary"] {
        background-color: #e8f4fd;
        border-color: #2196f3;
        color: #1976d2;

        &:hover {
          background-color: #bbdefb;
        }
      }

      &:not([color]) {
        background-color: #f8f9fa;
        border-color: #e0e0e0;
        color: #555;

        &:hover {
          background-color: #e9ecef;
        }
      }
    }
  }
}

// Loading, Error States
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 20px;
    color: #999;
  }

  p {
    color: #666;
    font-size: 16px;
    margin: 0 0 20px 0;
    font-weight: 500;
  }

  button {
    margin-top: 12px;
    height: 44px;
    border-radius: 8px;
    font-weight: 500;
  }
}

.error-container {
  mat-icon {
    color: #f44336;
  }

  p {
    color: #f44336;
  }
}

// Main Content
.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// Profile Overview Card
.profile-overview {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  overflow: hidden;

  mat-card-content {
    padding: 32px;
  }

  .profile-header {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 32px;
    align-items: start;
  }

  .profile-picture {
    width: 120px;
    height: 120px;
    border-radius: 16px;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    // Fallback when no image
    &:empty::before {
      content: '👤';
      font-size: 48px;
      color: white;
    }
  }

  .profile-info {
    min-width: 0; // Prevents flex item from overflowing

    h2 {
      font-size: 32px;
      font-weight: 700;
      color: #2c3e50;
      margin: 0 0 8px 0;
      letter-spacing: -0.5px;
    }

    .designation {
      font-size: 16px;
      color: #7f8c8d;
      font-weight: 500;
      margin: 0 0 20px 0;
    }

    .status-badges {
      margin-bottom: 20px;

      mat-chip-set {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      mat-chip {
        font-weight: 500;
        font-size: 12px;
        border-radius: 20px;
        padding: 8px 16px;
        height: auto;
        border: 1px solid;

        mat-icon {
          margin-right: 6px;
          font-size: 16px;
        }

        &.status-active {
          background-color: #d4edda;
          color: #155724;
          border-color: #c3e6cb;
        }

        &.status-inactive {
          background-color: #f8d7da;
          color: #721c24;
          border-color: #f5c6cb;
        }

        &.employment-type {
          background-color: #e3f2fd;
          color: #1976d2;
          border-color: #bbdefb;
        }
      }
    }

    .quick-actions {
      display: flex;
      gap: 12px;

      button {
        width: 44px;
        height: 44px;
        border-radius: 10px;
        background-color: #f8f9fa;
        border: 1px solid #e0e0e0;
        transition: all 0.2s ease;

        &:hover {
          background-color: #e3f2fd;
          border-color: #2196f3;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }

        mat-icon {
          color: #666;
          transition: color 0.2s ease;
        }

        &:hover mat-icon {
          color: #2196f3;
        }
      }
    }
  }

  .profile-stats {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .stat-item {
      text-align: center;
      padding: 16px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;
      border: 1px solid #e0e0e0;
      min-width: 100px;

      .stat-label {
        display: block;
        font-size: 12px;
        color: #6c757d;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 8px;
      }

      .stat-value {
        display: block;
        font-size: 18px;
        font-weight: 700;
        color: #2c3e50;
      }
    }
  }
}

// Detail Tabs Card
.detail-tabs {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  overflow: hidden;

  mat-card-content {
    padding: 0;
  }

  // Tab styling
  ::ng-deep .mat-mdc-tab-group {
    .mat-mdc-tab-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #e0e0e0;

      .mat-mdc-tab {
        padding: 0 24px;
        height: 60px;
        font-weight: 500;
        font-size: 14px;
        text-transform: none;
        opacity: 0.7;
        transition: all 0.2s ease;

        &.mdc-tab--active {
          opacity: 1;
          color: #2196f3;
        }
      }

      .mat-mdc-tab-ink-bar {
        background-color: #2196f3;
        height: 3px;
      }
    }

    .mat-mdc-tab-body-wrapper {
      .mat-mdc-tab-body {
        .mat-mdc-tab-body-content {
          padding: 0;
        }
      }
    }
  }

  .tab-content {
    padding: 32px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 20px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #e9ecef;
    }

    mat-divider {
      margin: 32px 0;
      background-color: #e9ecef;
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;

    .info-item {
      display: flex;
      flex-direction: column;
      gap: 6px;
      padding: 16px;
      background-color: #fafafa;
      border-radius: 8px;
      border: 1px solid #f0f0f0;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f5f5f5;
        border-color: #e0e0e0;
      }

      &.full-width {
        grid-column: 1 / -1;
      }

      label {
        font-size: 12px;
        font-weight: 600;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 4px;
      }

      span {
        font-size: 14px;
        color: #2c3e50;
        font-weight: 500;
        word-break: break-word;
        display: flex;
        align-items: center;
        gap: 8px;

        &.status-text {
          font-weight: 600;
          text-transform: uppercase;
          font-size: 12px;
          padding: 4px 12px;
          border-radius: 16px;
          display: inline-block;
          width: fit-content;

          &.active {
            background-color: #d4edda;
            color: #155724;
          }

          &.inactive {
            background-color: #f8d7da;
            color: #721c24;
          }
        }
      }

      .link {
        color: #2196f3;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;

        &:hover {
          color: #1976d2;
          text-decoration: underline;
        }
      }

      .inline-action {
        width: 32px;
        height: 32px;
        margin-left: auto;
        background-color: #e3f2fd;
        border: 1px solid #bbdefb;

        &:hover {
          background-color: #bbdefb;
        }

        mat-icon {
          font-size: 16px;
          color: #1976d2;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .staff-detail-container {
    padding: 16px;
  }

  .profile-overview .profile-header {
    grid-template-columns: auto 1fr;
    gap: 24px;

    .profile-stats {
      grid-column: 1 / -1;
      flex-direction: row;
      justify-content: center;
      margin-top: 20px;
    }
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .staff-detail-container {
    padding: 12px;
  }

  .detail-header {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
    padding: 16px;

    .header-left {
      justify-content: flex-start;
    }

    .header-actions {
      flex-direction: row;
      justify-content: center;
      flex-wrap: wrap;
      gap: 8px;

      button {
        min-width: auto;
        flex: 1;
        min-width: 140px;
      }
    }
  }

  .profile-overview {
    .profile-header {
      grid-template-columns: 1fr;
      text-align: center;
      gap: 20px;
    }

    .profile-picture {
      width: 100px;
      height: 100px;
      margin: 0 auto;
    }

    .profile-info {
      h2 {
        font-size: 24px;
      }
    }

    .profile-stats {
      flex-direction: row;
      justify-content: space-around;
      gap: 12px;

      .stat-item {
        min-width: 80px;
        padding: 12px 8px;

        .stat-value {
          font-size: 16px;
        }
      }
    }
  }

  .detail-tabs {
    .tab-content {
      padding: 20px 16px;

      h3 {
        font-size: 16px;
      }
    }

    ::ng-deep .mat-mdc-tab-group .mat-mdc-tab-header .mat-mdc-tab {
      padding: 0 12px;
      font-size: 13px;
    }
  }

  .info-grid .info-item {
    padding: 12px;

    span {
      font-size: 13px;
    }
  }
}

@media (max-width: 480px) {
  .detail-header {
    .header-left h1 {
      font-size: 20px;
    }

    .header-actions button {
      min-width: 100px;
      font-size: 12px;
      height: 40px;
    }
  }

  .profile-overview {
    mat-card-content {
      padding: 20px 16px;
    }

    .profile-picture {
      width: 80px;
      height: 80px;
    }

    .profile-info h2 {
      font-size: 20px;
    }

    .profile-stats {
      gap: 8px;

      .stat-item {
        padding: 8px 6px;
        min-width: 70px;

        .stat-label {
          font-size: 10px;
        }

        .stat-value {
          font-size: 14px;
        }
      }
    }
  }

  .detail-tabs .tab-content {
    padding: 16px 12px;
  }
}
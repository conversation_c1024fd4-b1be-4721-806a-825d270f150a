import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatDividerModule } from '@angular/material/divider';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PermissionService } from '../../core/services/permission.service';
import { RoleService } from '../../core/services/role.service';
import { ModuleService } from '../../core/services/module.service';
import { Role } from '../../core/models/user';
import { Module } from '../../core/models/module';
import { Permission, RoleModulePermission } from '../../core/models/permission';
import { ConfirmDialogComponent } from '../../shared/components/confirm-dialog/confirm-dialog.component';
import { finalize } from 'rxjs/operators';
import { Title } from '@angular/platform-browser';
import { MatChipListbox } from '@angular/material/chips';

@Component({
  selector: 'app-permission-management',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatCheckboxModule,
    MatTabsModule,
    MatExpansionModule,
    MatDividerModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTooltipModule,
    FormsModule,
    ReactiveFormsModule,
    MatChipListbox,
    // ConfirmDialogComponent is used programmatically with MatDialog in removeAllPermissions method
    ConfirmDialogComponent,
  ],
  templateUrl: './permission-management.component.html',
  styleUrl: './permission-management.component.scss',
})
export class PermissionManagementComponent implements OnInit {
  roles: Role[] = [];
  modules: Module[] = [];
  permissions: Permission[] = [];
  roleModulePermissions: RoleModulePermission[] = [];

  selectedRole: Role | null = null;
  selectedModule: Module | null = null;

  // Track assigned permissions with a Set for better performance
  assignedPermissionIds: Set<number> = new Set();
  pendingChanges: boolean = false;

  isLoading = true;
  errorMessage = '';

  constructor(
    private permissionService: PermissionService,
    private roleService: RoleService,
    private moduleService: ModuleService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private titleService: Title
  ) {
    this.titleService.setTitle('Permission Management');
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  loadInitialData(): void {
    this.isLoading = true;
    this.clearError();

    // Load roles and modules in parallel
    Promise.all([
      this.loadRoles(),
      this.loadModules(),
      this.loadPermissions(),
    ]).finally(() => {
      this.isLoading = false;
    });
  }

  loadRoles(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.roleService.getRoles(true).subscribe({
        next: (response) => {
          this.roles = response.data;
          console.log(`Loaded ${this.roles.length} roles`);
          resolve();
        },
        error: (error) => {
          this.errorMessage =
            'Error loading roles: ' + this.getErrorMessage(error);
          console.error('Error loading roles:', error);
          reject(error);
        },
      });
    });
  }

  loadModules(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.moduleService.getModules(true).subscribe({
        next: (response) => {
          this.modules = response.data;
          console.log(`Loaded ${this.modules.length} modules`);
          resolve();
        },
        error: (error) => {
          this.errorMessage =
            'Error loading modules: ' + this.getErrorMessage(error);
          console.error('Error loading modules:', error);
          reject(error);
        },
      });
    });
  }

  loadPermissions(): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log('Loading permissions...');
      this.permissionService.getPermissions().subscribe({
        next: (response) => {
          console.log('Permissions loaded successfully:', response);
          this.permissions = response.data;
          console.log(`Loaded ${this.permissions.length} permissions`);
          resolve();
        },
        error: (error) => {
          console.error('Error loading permissions:', error);
          this.errorMessage =
            'Error loading permissions: ' + this.getErrorMessage(error);

          // Set default permissions if API fails
          if (this.permissions.length === 0) {
            this.permissions = [
              { id: 1, name: 'create', description: 'Create new resources' },
              { id: 2, name: 'read', description: 'View existing resources' },
              {
                id: 3,
                name: 'update',
                description: 'Modify existing resources',
              },
              {
                id: 4,
                name: 'delete',
                description: 'Remove existing resources',
              },
            ];
            console.log('Using default permissions:', this.permissions);
          }
          reject(error);
        },
      });
    });
  }

  onRoleSelect(role: Role): void {
    this.selectedRole = role;
    this.selectedModule = null; // Reset module selection
    this.clearPermissionState();
  }

  onModuleSelect(module: Module): void {
    this.selectedModule = module;
    if (this.selectedRole) {
      this.loadRoleModulePermissions();
    }
  }

  clearPermissionState(): void {
    this.assignedPermissionIds.clear();
    this.pendingChanges = false;
  }

  loadRoleModulePermissions(): void {
    if (!this.selectedRole || !this.selectedModule) return;

    console.log(
      `Loading permissions for role ${this.selectedRole.id} (${this.selectedRole.name}) and module ${this.selectedModule.id} (${this.selectedModule.name})...`
    );

    this.isLoading = true;
    this.clearPermissionState();
    this.clearError();

    this.permissionService
      .getRoleModulePermissions(this.selectedRole.id, this.selectedModule.id)
      .subscribe({
        next: (response) => {
          console.log(`Role-module permissions loaded successfully:`, response);

          // Ensure response.data is an array or an object with permissions
          if (response && response.data) {
            // Store the raw response for debugging
            this.roleModulePermissions = Array.isArray(response.data)
              ? response.data
              : [];

            // Clear any existing permissions
            this.assignedPermissionIds.clear();

            // Handle different response structures
            if (
              response.data.permissions &&
              Array.isArray(response.data.permissions)
            ) {
              // Handle nested permissions structure
              console.log(
                'Found nested permissions array:',
                response.data.permissions
              );
              response.data.permissions.forEach((permission: any) => {
                if (permission.id) {
                  this.assignedPermissionIds.add(permission.id);
                  console.log(
                    `Added permission ID ${permission.id} from nested structure`
                  );
                }
              });
            } else if (Array.isArray(response.data)) {
              // Process the permissions array
              response.data.forEach((rmp) => {
                // Try different properties that might contain the permission ID
                if (rmp.permissionId) {
                  this.assignedPermissionIds.add(rmp.permissionId);
                  console.log(
                    `Added permission ID ${rmp.permissionId} from permissionId property`
                  );
                } else if (rmp.permission && rmp.permission.id) {
                  this.assignedPermissionIds.add(rmp.permission.id);
                  console.log(
                    `Added permission ID ${rmp.permission.id} from permission.id property`
                  );
                } else if (
                  rmp.id &&
                  this.permissions.some((p) => p.id === rmp.id)
                ) {
                  // This might be a direct permission object
                  this.assignedPermissionIds.add(rmp.id);
                  console.log(
                    `Added permission ID ${rmp.id} from direct id property`
                  );
                } else if (rmp.permissionName) {
                  // Try to find by name if ID is not available
                  const permission = this.permissions.find(
                    (p) => p.name === rmp.permissionName
                  );
                  if (permission) {
                    this.assignedPermissionIds.add(permission.id);
                    console.log(
                      `Added permission ID ${permission.id} by matching name ${rmp.permissionName}`
                    );
                  }
                } else if (rmp.name) {
                  // As a last resort, try to match by the name property
                  const permission = this.permissions.find(
                    (p) => p.name === rmp.name
                  );
                  if (permission) {
                    this.assignedPermissionIds.add(permission.id);
                    console.log(
                      `Added permission ID ${permission.id} by matching name ${rmp.name}`
                    );
                  }
                }
              });
            }

            // Check for permissions in the role-module level structure
            if (
              response.data.role &&
              response.data.module &&
              response.data.permissions
            ) {
              console.log('Found role-module structure with permissions');
              const permissions = response.data.permissions;
              if (Array.isArray(permissions)) {
                permissions.forEach((p) => {
                  if (p.id) {
                    this.assignedPermissionIds.add(p.id);
                    console.log(
                      `Added permission ID ${p.id} from role-module-level permissions`
                    );
                  }
                });
              }
            }

            console.log(
              `Loaded ${this.roleModulePermissions.length} role-module permissions with ${this.assignedPermissionIds.size} assigned IDs`
            );
            console.log('Assigned permission IDs:', [
              ...this.assignedPermissionIds,
            ]);
          } else {
            console.error('Invalid response format:', response);
            this.roleModulePermissions = [];
          }

          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading role-module permissions:', error);
          this.errorMessage =
            'Error loading role-module permissions: ' +
            this.getErrorMessage(error);
          this.isLoading = false;

          // Clear permissions on error
          this.roleModulePermissions = [];
          this.assignedPermissionIds.clear();
        },
      });
  }

  toggleCheckbox(permissionId: number): void {
    if (this.isLoading) return;

    // Find the checkbox element and toggle it programmatically
    // This is a workaround for when users click on the label area instead of the checkbox itself
    const hasPermission = this.assignedPermissionIds.has(permissionId);
    this.togglePermission(permissionId, hasPermission);
  }

  togglePermission(permissionId: number, hasPermission: boolean): void {
    if (!this.selectedRole || !this.selectedModule || this.isLoading) return;

    // Find the permission
    const permission = this.permissions.find((p) => p.id === permissionId);
    if (!permission) {
      console.error(`Permission with ID ${permissionId} not found`);
      return;
    }

    const permissionName = permission.name || 'permission';
    console.log(
      `Toggling ${permissionName} permission (currently ${
        hasPermission ? 'assigned' : 'not assigned'
      })`
    );

    // Immediately update UI state for better user experience
    if (hasPermission) {
      this.assignedPermissionIds.delete(permissionId);
    } else {
      this.assignedPermissionIds.add(permissionId);
    }

    // Set loading state
    this.isLoading = true;
    this.clearError();

    if (hasPermission) {
      // Remove permission
      this.permissionService
        .removePermission(
          this.selectedRole.id,
          this.selectedModule.id,
          permissionId
        )
        .pipe(
          finalize(() => {
            // Always ensure loading state is reset
            this.isLoading = false;
          })
        )
        .subscribe({
          next: (response) => {
            console.log(`Permission removed successfully:`, response);
            // Make sure our local state reflects the server state
            this.assignedPermissionIds.delete(permissionId);
            this.showNotification(
              `${permissionName} permission removed successfully`
            );
          },
          error: (error) => {
            console.error('Error removing permission:', error);

            // Revert UI state on error
            this.assignedPermissionIds.add(permissionId);

            // Handle 404 Not Found specially (permission might already be removed)
            if (error.status === 404) {
              console.log(
                'Permission not found (404), might already be removed'
              );
              // Despite the error, the permission is not assigned, so update the UI
              this.assignedPermissionIds.delete(permissionId);
              this.showNotification(
                `${permissionName} permission is already removed`,
                'success'
              );
              return;
            }

            this.errorMessage =
              'Error removing permission: ' + this.getErrorMessage(error);
            this.showNotification(
              `Error removing ${permissionName} permission`,
              'error'
            );
          },
        });
    } else {
      // Add permission
      this.permissionService
        .assignPermission(
          this.selectedRole.id,
          this.selectedModule.id,
          permissionId
        )
        .pipe(
          finalize(() => {
            // Always ensure loading state is reset
            this.isLoading = false;
          })
        )
        .subscribe({
          next: (response) => {
            console.log(`Permission assigned successfully:`, response);
            // Make sure our local state reflects the server state
            this.assignedPermissionIds.add(permissionId);
            this.showNotification(
              `${permissionName} permission assigned successfully`
            );
          },
          error: (error) => {
            console.error('Error assigning permission:', error);

            // Revert UI state on error
            this.assignedPermissionIds.delete(permissionId);

            // Handle 409 Conflict specially
            if (error.status === 409) {
              console.log('Permission already exists (409 Conflict)');
              // Despite the error, the permission is assigned, so update the UI
              this.assignedPermissionIds.add(permissionId);
              this.showNotification(
                `${permissionName} permission is already assigned`,
                'success'
              );
              return;
            }

            this.errorMessage =
              'Error assigning permission: ' + this.getErrorMessage(error);
            this.showNotification(
              `Error assigning ${permissionName} permission`,
              'error'
            );
          },
        });
    }
  }

  // Helper method to show notifications
  private showNotification(
    message: string,
    type: 'success' | 'error' = 'success'
  ): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: type === 'error' ? ['error-snackbar'] : ['success-snackbar'],
    });
  }

  // Extract error message from error object
  private getErrorMessage(error: any): string {
    return (
      error.message ||
      error.statusText ||
      error.error?.message ||
      'Unknown error'
    );
  }

  // Clear error message
  clearError(): void {
    this.errorMessage = '';
  }

  assignAllPermissions(): void {
    if (!this.selectedRole || !this.selectedModule || this.isLoading) return;

    this.isLoading = true;
    this.clearError();

    // First update the UI optimistically for better user experience
    this.permissions.forEach((permission) => {
      this.assignedPermissionIds.add(permission.id);
    });

    this.permissionService
      .assignAllModulePermissions(this.selectedRole.id, this.selectedModule.id)
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: (response) => {
          console.log('All permissions assigned successfully:', response);

          // Reload the permissions to ensure we have the correct state from server
          this.loadRoleModulePermissions();

          this.showNotification(
            `All permissions assigned to ${this.selectedRole?.name} for ${this.selectedModule?.name}`
          );
        },
        error: (error) => {
          console.error('Error assigning all permissions:', error);

          // If the API call failed, we need to reload to get the current state
          this.loadRoleModulePermissions();

          this.errorMessage =
            'Error assigning all permissions: ' + this.getErrorMessage(error);
          this.showNotification(`Error assigning all permissions`, 'error');
        },
      });
  }

  removeAllPermissions(): void {
    if (!this.selectedRole || !this.selectedModule || this.isLoading) return;

    // Show confirmation dialog
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '350px',
      data: {
        title: 'Confirm Removal',
        message: `Are you sure you want to remove all permissions from ${this.selectedRole.name} for ${this.selectedModule.name}?`,
        confirmText: 'Remove All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.isLoading = true;
        this.clearError();

        // Update UI optimistically
        this.assignedPermissionIds.clear();

        this.permissionService
          .removeAllModulePermissions(
            this.selectedRole!.id,
            this.selectedModule!.id
          )
          .pipe(
            finalize(() => {
              this.isLoading = false;
            })
          )
          .subscribe({
            next: (response) => {
              console.log('All permissions removed successfully:', response);

              // Reload to ensure we have the correct state
              this.loadRoleModulePermissions();

              this.showNotification(
                `All permissions removed from ${this.selectedRole?.name} for ${this.selectedModule?.name}`
              );
            },
            error: (error) => {
              console.error('Error removing all permissions:', error);

              // If the API call failed, we need to reload to get the current state
              this.loadRoleModulePermissions();

              this.errorMessage =
                'Error removing all permissions: ' +
                this.getErrorMessage(error);
              this.showNotification(`Error removing all permissions`, 'error');
            },
          });
      }
    });
  }
}

.permission-management-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    margin-bottom: 24px;
    
    h1 {
      font-size: 28px;
      font-weight: 500;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    .description {
      color: #666;
      font-size: 16px;
      margin: 0;
    }
  }

  .selection-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  .permissions-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    mat-card-header {
      padding: 16px;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 24px;

    span {
      margin-top: 16px;
      color: #666;
      font-size: 16px;
    }
  }

  .error-message {
    color: #f44336;
    margin: 0 0 24px 0;
    padding: 12px 16px;
    background-color: #ffeeee;
    border-radius: 8px;
    display: flex;
    align-items: center;
    border-left: 4px solid #f44336;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    mat-icon {
      margin-right: 12px;
      color: #f44336;
    }

    span {
      flex: 1;
    }

    button {
      margin-left: 8px;
    }
  }

  .selection-container {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    padding: 8px;

    .role-selection, .module-selection {
      flex: 1;
      min-width: 250px;
    }

    .module-selection.disabled {
      opacity: 0.7;
    }

    mat-form-field {
      width: 100%;
    }
  }

  .permissions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .header-title {
      display: flex;
      align-items: center;

      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 500;
      }

      .section-icon {
        margin-right: 12px;
        color: #3f51b5;
      }
    }

    .bulk-actions {
      display: flex;
      gap: 12px;
    }
  }

  .permissions-list {
    padding: 16px;

    .no-permissions {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px;
      color: #666;
      text-align: center;

      .empty-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        margin-bottom: 16px;
        color: #ccc;
      }

      p {
        font-size: 16px;
      }
    }

    .permissions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 16px;
    }

    .permission-item {
      background-color: #f9f9f9;
      padding: 16px;
      border-radius: 8px;
      transition: all 0.2s ease;
      display: flex;
      align-items: flex-start;
      border: 1px solid #eee;

      &:hover {
        background-color: #f5f5f5;
        border-color: #ddd;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.05);
      }

      &.assigned {
        background-color: rgba(63, 81, 181, 0.05);
        border-color: rgba(63, 81, 181, 0.2);
      }

      .permission-details {
        display: flex;
        flex-direction: column;
        margin-left: 12px;
        flex: 1;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;

        &:hover {
          background-color: rgba(0,0,0,0.02);
        }
      }

      .permission-name-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }

      .permission-name {
        font-weight: 500;
        font-size: 16px;
        color: #333;
      }

      .permission-description {
        color: #666;
        font-size: 14px;
        margin: 0;
        line-height: 1.4;
      }

      .permission-checkbox {
        margin-top: 2px;

        ::ng-deep .mat-checkbox-frame {
          border-width: 2px;
        }

        ::ng-deep .mat-checkbox-checked .mat-checkbox-background {
          background-color: #3f51b5;
        }
      }
    }
  }

  .assigned-chip {
    background-color: #4caf50 !important;
    color: white !important;
    font-size: 12px !important;
    height: 24px !important;
    min-height: 24px !important;
  }

  .not-assigned-chip {
    background-color: #f44336 !important;
    color: white !important;
    font-size: 12px !important;
    height: 24px !important;
    min-height: 24px !important;
  }

  .action-button {
    min-width: 120px;
    
    mat-icon {
      margin-right: 8px;
    }
  }

  .no-selection {
    text-align: center;
    background-color: white;
    border-radius: 8px;
    padding: 48px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    
    .large-icon {
      font-size: 64px;
      height: 64px;
      width: 64px;
      color: #ccc;
      margin-bottom: 16px;
    }
    
    h3 {
      font-size: 20px;
      color: #333;
      margin-bottom: 8px;
    }
    
    p {
      color: #666;
      font-size: 16px;
      max-width: 400px;
      margin: 0 auto;
    }
  }
}

// Add these styles for the snackbar notifications
::ng-deep .success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

::ng-deep .error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}
.profile-container {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 24px;
  color: #333;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  padding: 48px 0;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 24px;
}

.profile-content {
  animation: fadeIn 0.3s ease-in-out;
}

.profile-card {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.tab-content {
  padding: 24px 16px;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: 500;
  margin-right: 24px;
}

.profile-info {
  h2 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 500;
  }
}

.role-badge {
  display: inline-block;
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
  margin-bottom: 8px;
}

.profile-form,
.password-form {
  margin-top: 24px;
}

.form-row {
  margin-bottom: 16px;
  
  mat-form-field {
    width: 100%;
  }
}

.form-actions {
  display: flex;
  align-items: center;
  margin-top: 24px;
  
  button {
    min-width: 150px;
    
    mat-icon {
      margin-right: 8px;
    }
  }
  
  .button-spinner {
    margin-left: 16px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

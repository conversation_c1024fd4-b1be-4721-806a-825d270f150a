<!-- src/app/features/module-management/module-form/module-form.component.html -->

<div class="module-form-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit Module' : 'Add New Module' }}</mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-spinner">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <div *ngIf="errorMessage" class="error-message">
        <mat-icon>error</mat-icon> {{ errorMessage }}
        <div *ngIf="errorMessage.includes('500')">
          <p>This could be due to:</p>
          <ul>
            <li>Database connection issues</li>
            <li>Server configuration problems</li>
            <li>Backend service unavailability</li>
          </ul>
          <div class="error-actions">
            <button mat-raised-button color="primary" (click)="retryLoading()" *ngIf="isEditMode">
              <mat-icon>refresh</mat-icon> Retry Loading
            </button>
            <a mat-button [routerLink]="['/admin/modules']">
              <mat-icon>list</mat-icon> Return to Modules List
            </a>
          </div>
        </div>
      </div>

      <form [formGroup]="moduleForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter module name">
            <mat-error *ngIf="moduleForm.get('name')?.hasError('required')">
              Name is required
            </mat-error>
            <mat-error *ngIf="moduleForm.get('name')?.hasError('minlength')">
              Name must be at least 3 characters
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Description</mat-label>
            <textarea matInput formControlName="description" placeholder="Enter module description" rows="3"></textarea>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-slide-toggle formControlName="is_active" color="primary">
            {{ moduleForm.get('is_active')?.value ? 'Active' : 'Inactive' }}
          </mat-slide-toggle>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions align="end">
      <button mat-button type="button" (click)="cancel()">Cancel</button>
      <button mat-raised-button color="primary" [disabled]="isLoading || moduleForm.invalid" (click)="onSubmit()">
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </mat-card-actions>
  </mat-card>
</div>

<!-- src/app/features/module-management/module-detail/module-detail.component.html -->

<div class="module-detail-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>Module Details</mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-spinner">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <div *ngIf="module && !isLoading" class="module-info">
        <div class="module-header">
          <h2>{{ module.name }}</h2>
          <div class="module-status">
            <mat-chip [color]="module.is_active ? 'primary' : 'warn'" selected>
              {{ module.is_active ? 'Active' : 'Inactive' }}
            </mat-chip>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="module-details">
          <div class="detail-item">
            <span class="label">ID:</span>
            <span class="value">{{ module.id }}</span>
          </div>

          <div class="detail-item">
            <span class="label">Description:</span>
            <span class="value">{{ module.description || 'No description provided' }}</span>
          </div>

          <div class="detail-item">
            <span class="label">Created At:</span>
            <span class="value">{{ module.created_at | date:'medium' }}</span>
          </div>

          <div class="detail-item" *ngIf="module.updated_at">
            <span class="label">Last Updated:</span>
            <span class="value">{{ module.updated_at | date:'medium' }}</span>
          </div>

          <div class="detail-item" *ngIf="module.created_by_username">
            <span class="label">Created By:</span>
            <span class="value">{{ module.created_by_username }}</span>
          </div>
        </div>
      </div>
    </mat-card-content>

    <mat-card-actions align="end" *ngIf="module">
      <button mat-button (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> Back
      </button>
      <button mat-raised-button color="warn" (click)="deleteModule()">
        <mat-icon>delete</mat-icon> Delete
      </button>
      <button mat-raised-button color="accent" (click)="toggleModuleStatus()">
        <mat-icon>{{ module.is_active ? 'toggle_off' : 'toggle_on' }}</mat-icon>
        {{ module.is_active ? 'Deactivate' : 'Activate' }}
      </button>
      <button mat-raised-button color="primary" (click)="editModule()">
        <mat-icon>edit</mat-icon> Edit
      </button>
    </mat-card-actions>
  </mat-card>
</div>

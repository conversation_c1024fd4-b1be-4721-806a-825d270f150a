.module-detail-container {
  padding: 20px;

  mat-card {
    margin-bottom: 20px;
  }

  .loading-spinner {
    display: flex;
    justify-content: center;
    margin: 20px 0;
  }

  .error-message {
    color: red;
    margin: 10px 0;
    padding: 10px;
    background-color: #ffeeee;
    border-radius: 4px;
  }

  .module-info {
    padding: 16px 0;

    .module-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h2 {
        margin: 0;
        font-size: 24px;
      }
    }

    .module-details {
      margin-top: 16px;

      .detail-item {
        margin-bottom: 12px;
        display: flex;

        .label {
          font-weight: 500;
          min-width: 120px;
          color: #666;
        }

        .value {
          flex: 1;
        }
      }
    }
  }

  mat-card-actions {
    padding: 16px;
    display: flex;
    gap: 8px;
  }
}

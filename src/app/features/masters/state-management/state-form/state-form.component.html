<div class="state-form-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit State' : 'Add New State' }}</mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-spinner">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <form [formGroup]="stateForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>State Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter state name">
            <mat-error *ngIf="nameControl?.hasError('required')">
              State name is required
            </mat-error>
            <mat-error *ngIf="nameControl?.hasError('minlength')">
              State name must be at least 2 characters
            </mat-error>
            <mat-error *ngIf="nameControl?.hasError('maxlength')">
              State name cannot exceed 100 characters
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>State Code</mat-label>
            <input matInput formControlName="code" placeholder="Enter state code (e.g., CA, TX)">
            <mat-error *ngIf="codeControl?.hasError('required')">
              State code is required
            </mat-error>
            <mat-error *ngIf="codeControl?.hasError('minlength')">
              State code must be at least 1 character
            </mat-error>
            <mat-error *ngIf="codeControl?.hasError('maxlength')">
              State code cannot exceed 10 characters
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Country</mat-label>
            <mat-select formControlName="country_id">
              <mat-option *ngFor="let country of countries" [value]="country.id">
                {{ country.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="countryIdControl?.hasError('required')">
              Country is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-slide-toggle formControlName="is_active" color="primary">
            {{ stateForm.get('is_active')?.value ? 'Active' : 'Inactive' }}
          </mat-slide-toggle>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions align="end">
      <button mat-button type="button" routerLink="..">Cancel</button>
      <button mat-raised-button color="primary" [disabled]="isLoading || stateForm.invalid" (click)="onSubmit()">
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </mat-card-actions>
  </mat-card>
</div>

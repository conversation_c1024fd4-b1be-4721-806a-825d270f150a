<div class="state-detail-container">
  <div class="detail-header">
    <h1>State Details</h1>
    <button mat-button color="primary" (click)="goBack()">
      <mat-icon>arrow_back</mat-icon> Back to States
    </button>
  </div>

  <mat-card>
    <mat-card-content>
      <!-- Loading spinner -->
      <div class="loading-spinner" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading state details...</p>
      </div>

      <!-- Error message -->
      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon> {{ errorMessage }}
      </div>

      <!-- State details -->
      <div class="state-info" *ngIf="!isLoading && state">
        <div class="state-header">
          <h2>{{ state.name }}</h2>
          <mat-chip-set>
            <mat-chip [color]="state.is_active ? 'primary' : 'warn'" selected>
              {{ state.is_active ? 'Active' : 'Inactive' }}
            </mat-chip>
          </mat-chip-set>
        </div>

        <mat-divider></mat-divider>

        <div class="state-details">
          <div class="detail-item">
            <span class="label">ID:</span>
            <span class="value">{{ state.id }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Name:</span>
            <span class="value">{{ state.name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Code:</span>
            <span class="value">{{ state.code }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Country:</span>
            <span class="value">{{ state.country_name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Status:</span>
            <span class="value">{{ state.is_active ? 'Active' : 'Inactive' }}</span>
          </div>
          <div class="detail-item" *ngIf="state.created_by_username">
            <span class="label">Created By:</span>
            <span class="value">{{ state.created_by_username }}</span>
          </div>
          <div class="detail-item" *ngIf="state.created_at">
            <span class="label">Created At:</span>
            <span class="value">{{ state.created_at | date:'medium' }}</span>
          </div>
          <div class="detail-item" *ngIf="state.updated_by_username">
            <span class="label">Updated By:</span>
            <span class="value">{{ state.updated_by_username }}</span>
          </div>
          <div class="detail-item" *ngIf="state.updated_at">
            <span class="label">Updated At:</span>
            <span class="value">{{ state.updated_at | date:'medium' }}</span>
          </div>
        </div>

        <div class="detail-actions" *ngIf="!isLoading && state">
          <button mat-raised-button color="warn" (click)="deleteState()">
            <mat-icon>delete</mat-icon> Delete
          </button>
          <button mat-raised-button color="accent" (click)="toggleStatus()">
            <mat-icon>{{ state.is_active ? 'toggle_off' : 'toggle_on' }}</mat-icon>
            {{ state.is_active ? 'Deactivate' : 'Activate' }}
          </button>
          <button mat-raised-button color="primary" [routerLink]="['../../edit', state.id]">
            <mat-icon>edit</mat-icon> Edit
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>

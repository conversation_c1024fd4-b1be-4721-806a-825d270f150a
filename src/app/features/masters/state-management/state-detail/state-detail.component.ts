import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

import { StateService } from '../../../../core/services/masters/state.service';
import { State } from '../../../../core/models/masters/state';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-state-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatDividerModule,
    MatSnackBarModule,
    MatDialogModule
  ],
  templateUrl: './state-detail.component.html',
  styleUrls: ['./state-detail.component.scss']
})
export class StateDetailComponent implements OnInit {
  state: State | null = null;
  isLoading = true;
  errorMessage = '';

  constructor(
    private stateService: StateService,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.loadStateData(+id);
      } else {
        this.errorMessage = 'No state ID provided';
        this.isLoading = false;
      }
    });
  }

  loadStateData(id: number): void {
    this.isLoading = true;
    this.errorMessage = '';
    
    this.stateService.getStateById(id).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.state = response.data as State;
        } else {
          this.errorMessage = response.message || 'Failed to load state data';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading state data: ' + this.getErrorMessage(error);
        this.isLoading = false;
        console.error('Error loading state data:', error);
      }
    });
  }

  toggleStatus(): void {
    if (!this.state) return;
    
    const newStatus = !this.state.is_active;
    
    this.stateService.toggleStateStatus(this.state.id, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.state) {
            this.state.is_active = newStatus;
          }
          this.showSnackBar(`State ${newStatus ? 'activated' : 'deactivated'} successfully`);
        } else {
          this.showSnackBar(`Failed to update status: ${response.message}`, true);
        }
      },
      error: (error) => {
        this.showSnackBar(`Error updating status: ${this.getErrorMessage(error)}`, true);
        console.error('Error toggling state status:', error);
      }
    });
  }

  deleteState(): void {
    if (!this.state) return;
    
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the state "${this.state.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && this.state) {
        this.stateService.deleteState(this.state.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.showSnackBar(`State ${this.state?.name} deleted successfully`);
              this.router.navigate(['../../'], { relativeTo: this.route });
            } else {
              this.showSnackBar(`Failed to delete state: ${response.message}`, true);
            }
          },
          error: (error) => {
            this.showSnackBar(`Error deleting state: ${this.getErrorMessage(error)}`, true);
            console.error('Error deleting state:', error);
          }
        });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error occurred';
  }
}

@import 'styles/variables';
@import 'styles/mixins';

.department-detail-container {
  @include card-container;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: $text-color;
    }

    .header-actions {
      display: flex;
      gap: $spacing-md;
    }
  }

  mat-card {
    @include card-styling;
    background-color: $card-background;
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl 0;

    p {
      margin-top: $spacing-md;
      color: $text-secondary;
    }
  }

  .error-message {
    @include message-box($error-color);
  }

  .detail-content {
    .detail-header-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-lg;

      .detail-title {
        h2 {
          margin: 0 0 $spacing-xs 0;
          font-size: 20px;
          font-weight: 500;
        }

        .detail-code {
          font-size: 14px;
          color: $text-secondary;
          background-color: rgba($primary-color, 0.1);
          padding: 2px 8px;
          border-radius: 4px;
        }
      }

      .detail-status {
        font-weight: 500;

        .status-active {
          color: $success-color;
        }

        .status-inactive {
          color: $warning-color;
        }
      }
    }

    mat-divider {
      margin: $spacing-lg 0;
    }

    .detail-section {
      margin-bottom: $spacing-lg;

      h3 {
        margin: 0 0 $spacing-md 0;
        font-size: 16px;
        font-weight: 500;
        color: $text-color;
      }

      .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: $spacing-md;

        .detail-item {
          .detail-label {
            font-size: 12px;
            color: $text-secondary;
            margin-bottom: 4px;
          }

          .detail-value {
            font-size: 14px;
          }
        }
      }
    }

    .detail-actions {
      display: flex;
      justify-content: flex-end;
      gap: $spacing-md;
      margin-top: $spacing-xl;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .department-detail-container {
    .detail-header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;

      .header-actions {
        width: 100%;
        justify-content: space-between;
      }
    }

    .detail-content {
      .detail-header-section {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-md;

        .detail-status {
          align-self: flex-start;
        }
      }

      .detail-actions {
        flex-direction: column;
        width: 100%;

        button {
          width: 100%;
        }
      }
    }
  }
}

<div class="department-detail-container">
  <div class="detail-header">
    <h1>Department Details</h1>
    <div class="header-actions">
      <button mat-raised-button color="primary" routerLink="../">
        <mat-icon>arrow_back</mat-icon> Back to List
      </button>
      <button mat-raised-button color="accent" [routerLink]="['../edit', department.id]" *ngIf="department">
        <mat-icon>edit</mat-icon> Edit
      </button>
    </div>
  </div>

  <mat-card>
    <mat-card-content>
      <!-- Loading spinner -->
      <div class="loading-spinner" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading department details...</p>
      </div>

      <!-- Error message -->
      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon> {{ errorMessage }}
      </div>

      <!-- Department details -->
      <div class="detail-content" *ngIf="department && !isLoading">
        <div class="detail-header-section">
          <div class="detail-title">
            <h2>{{ department.name }}</h2>
            <span class="detail-code">{{ department.code }}</span>
          </div>
          <div class="detail-status">
            <span [ngClass]="getStatusClass()">{{ getStatusText() }}</span>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="detail-section">
          <h3>System Information</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <div class="detail-label">Created By</div>
              <div class="detail-value">{{ department.created_by_username || 'N/A' }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Created At</div>
              <div class="detail-value">{{ getFormattedDate(department.created_at) }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Updated By</div>
              <div class="detail-value">{{ department.updated_by_username || 'N/A' }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Updated At</div>
              <div class="detail-value">{{ getFormattedDate(department.updated_at) }}</div>
            </div>
          </div>
        </div>

        <div class="detail-actions">
          <button mat-raised-button color="primary" (click)="toggleStatus()" matTooltip="Change status">
            <mat-icon>{{ department.is_active ? 'toggle_off' : 'toggle_on' }}</mat-icon>
            {{ department.is_active ? 'Deactivate' : 'Activate' }}
          </button>
          <button mat-raised-button color="warn" (click)="deleteDepartment()" matTooltip="Delete this department">
            <mat-icon>delete</mat-icon> Delete
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>

import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';

import { DepartmentService } from '../../../../core/services/masters/department.service';
import { Department } from '../../../../core/models/masters/department';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-department-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  templateUrl: './department-list.component.html',
  styleUrls: ['./department-list.component.scss'],
})
export class DepartmentListComponent implements OnInit {
  departments: Department[] = [];
  filteredDepartments: Department[] = [];
  displayedDepartments: Department[] = []; // Departments after pagination
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'code',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<Department>(true, []); // Multiple selection model
  isLoading = true;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';
  includeInactive = false;

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalDepartments = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private departmentService: DepartmentService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadDepartments();
  }

  loadDepartments(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.departmentService
      .getDepartments(this.includeInactive)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.departments = Array.isArray(response.data) ? response.data : [];
            this.totalDepartments = this.departments.length;
            this.applyFilter();
          } else {
            this.errorMessage = response.message || 'Failed to load departments';
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage =
            'Error loading departments: ' + this.getErrorMessage(error);
          this.isLoading = false;
          console.error('Error loading departments:', error);
        },
      });
  }

  applyFilter(): void {
    // Filter departments based on search term
    const searchTermLower = this.searchTerm.toLowerCase().trim();
    
    this.filteredDepartments = this.departments.filter((department) => {
      return (
        department.name.toLowerCase().includes(searchTermLower) ||
        department.code.toLowerCase().includes(searchTermLower)
      );
    });

    this.totalDepartments = this.filteredDepartments.length;
    this.selection.clear();
    this.updateDisplayedDepartments();
  }

  updateDisplayedDepartments(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedDepartments = this.filteredDepartments.slice(startIndex, endIndex);
  }

  onPageChange(event: any): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedDepartments();
  }

  deleteDepartment(department: Department): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the department "${department.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.departmentService.deleteDepartment(department.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.successMessage = 'Department deleted successfully';
              this.showSnackBar(this.successMessage);
              this.loadDepartments();
            } else {
              this.errorMessage = response.message || 'Failed to delete department';
              this.showSnackBar(this.errorMessage, true);
            }
          },
          error: (error) => {
            this.errorMessage = 'Error deleting department: ' + this.getErrorMessage(error);
            this.showSnackBar(this.errorMessage, true);
            console.error('Error deleting department:', error);
          },
        });
      }
    });
  }

  bulkDeleteSelected(): void {
    if (this.selection.selected.length === 0) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${this.selection.selected.length} selected departments?`,
        confirmText: 'Delete All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = this.selection.selected.map((department) => department.id);
        
        this.departmentService.bulkDeleteDepartments(ids).subscribe({
          next: (response) => {
            if (response.success) {
              this.successMessage = 'Departments deleted successfully';
              this.showSnackBar(this.successMessage);
              this.selection.clear();
              this.loadDepartments();
            } else {
              this.errorMessage = response.message || 'Failed to delete departments';
              this.showSnackBar(this.errorMessage, true);
            }
          },
          error: (error) => {
            this.errorMessage = 'Error deleting departments: ' + this.getErrorMessage(error);
            this.showSnackBar(this.errorMessage, true);
            console.error('Error deleting departments:', error);
          },
        });
      }
    });
  }

  toggleStatus(department: Department): void {
    const newStatus = !department.is_active;
    
    this.departmentService.toggleDepartmentStatus(department.id, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          department.is_active = newStatus;
          this.successMessage = `Department ${newStatus ? 'activated' : 'deactivated'} successfully`;
          this.showSnackBar(this.successMessage);
        } else {
          this.errorMessage = response.message || 'Failed to update department status';
          this.showSnackBar(this.errorMessage, true);
        }
      },
      error: (error) => {
        this.errorMessage = 'Error updating department status: ' + this.getErrorMessage(error);
        this.showSnackBar(this.errorMessage, true);
        console.error('Error updating department status:', error);
      },
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error';
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedDepartments.length;
    return numSelected === numRows && numRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedDepartments);
    }
  }

  refreshList(): void {
    this.loadDepartments();
  }

  toggleIncludeInactive(): void {
    this.loadDepartments();
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

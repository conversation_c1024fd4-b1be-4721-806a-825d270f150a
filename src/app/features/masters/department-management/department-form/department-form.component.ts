import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { DepartmentService } from '../../../../core/services/masters/department.service';
import { Department } from '../../../../core/models/masters/department';

@Component({
  selector: 'app-department-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    MatSnackBarModule,
  ],
  templateUrl: './department-form.component.html',
  styleUrls: ['./department-form.component.scss'],
})
export class DepartmentFormComponent implements OnInit {
  departmentForm!: FormGroup;
  isEditMode = false;
  departmentId: number | null = null;
  isLoading = false;
  isSubmitting = false;
  errorMessage = '';
  pageTitle = 'Add New Department';
  submitButtonText = 'Create Department';

  constructor(
    private fb: FormBuilder,
    private departmentService: DepartmentService,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.createForm();
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.isEditMode = true;
        this.departmentId = +id;
        this.pageTitle = 'Edit Department';
        this.submitButtonText = 'Update Department';
        this.loadDepartment(+id);
      }
    });
  }

  createForm(): void {
    this.departmentForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      code: ['', [Validators.required, Validators.maxLength(20)]],
      is_active: [true]
    });
  }

  loadDepartment(id: number): void {
    this.isLoading = true;
    this.departmentService.getDepartmentById(id).subscribe({
      next: (response) => {
        if (response.success && !Array.isArray(response.data)) {
          const department = response.data;
          this.departmentForm.patchValue({
            name: department.name,
            code: department.code,
            is_active: department.is_active
          });
        } else {
          this.errorMessage = 'Failed to load department details';
          this.showSnackBar(this.errorMessage, true);
          this.router.navigate(['../'], { relativeTo: this.route });
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading department: ' + this.getErrorMessage(error);
        this.showSnackBar(this.errorMessage, true);
        this.isLoading = false;
        this.router.navigate(['../'], { relativeTo: this.route });
      }
    });
  }

  onSubmit(): void {
    if (this.departmentForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.departmentForm.controls).forEach(key => {
        const control = this.departmentForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    const departmentData: Partial<Department> = this.departmentForm.value;

    if (this.isEditMode && this.departmentId) {
      // Update existing department
      this.departmentService.updateDepartment(this.departmentId, departmentData).subscribe({
        next: (response) => {
          if (response.success) {
            this.showSnackBar('Department updated successfully');
            this.router.navigate(['../../'], { relativeTo: this.route });
          } else {
            this.errorMessage = response.message || 'Failed to update department';
            this.showSnackBar(this.errorMessage, true);
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          this.errorMessage = 'Error updating department: ' + this.getErrorMessage(error);
          this.showSnackBar(this.errorMessage, true);
          this.isSubmitting = false;
        }
      });
    } else {
      // Create new department
      this.departmentService.createDepartment(departmentData).subscribe({
        next: (response) => {
          if (response.success) {
            this.showSnackBar('Department created successfully');
            this.router.navigate(['../'], { relativeTo: this.route });
          } else {
            this.errorMessage = response.message || 'Failed to create department';
            this.showSnackBar(this.errorMessage, true);
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          this.errorMessage = 'Error creating department: ' + this.getErrorMessage(error);
          this.showSnackBar(this.errorMessage, true);
          this.isSubmitting = false;
        }
      });
    }
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error';
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  // Form validation helpers
  get nameControl() { return this.departmentForm.get('name'); }
  get codeControl() { return this.departmentForm.get('code'); }
}

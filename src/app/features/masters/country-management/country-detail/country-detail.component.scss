@import 'styles/variables';
@import 'styles/mixins';

.country-detail-container {
  @include card-container;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: $text-color;
    }
  }

  mat-card {
    @include card-styling;
    max-width: 800px;
    margin: 0 auto;
    background-color: $card-background;
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl 0;

    p {
      margin-top: $spacing-md;
      color: $text-secondary;
    }
  }

  .error-message {
    @include message-box($error-color);
  }

  .country-info {
    padding: $spacing-md 0;

    .country-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-md;

      h2 {
        margin: 0;
        font-size: 24px;
        color: $text-color;
      }
    }

    .country-details {
      margin: $spacing-md 0 $spacing-lg;

      .detail-item {
        @include detail-item;
      }
    }

    .detail-actions {
      display: flex;
      justify-content: flex-end;
      gap: $spacing-sm;
      margin-top: $spacing-lg;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .country-detail-container {
    padding: $spacing-md;

    .detail-header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;

      .header-actions {
        width: 100%;
        justify-content: space-between;
      }
    }

    .country-info {
      .country-header {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-sm;
      }

      .detail-actions {
        flex-direction: column;
        width: 100%;

        button {
          width: 100%;
          margin-bottom: $spacing-sm;
        }
      }
    }
  }
}

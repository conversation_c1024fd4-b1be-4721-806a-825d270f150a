<div class="country-form-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit Country' : 'Add New Country' }}</mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-spinner">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <form [formGroup]="countryForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Country Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter country name">
            <mat-error *ngIf="nameControl?.hasError('required')">
              Country name is required
            </mat-error>
            <mat-error *ngIf="nameControl?.hasError('minlength')">
              Country name must be at least 2 characters
            </mat-error>
            <mat-error *ngIf="nameControl?.hasError('maxlength')">
              Country name cannot exceed 100 characters
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Country Code</mat-label>
            <input matInput formControlName="code" placeholder="Enter country code (e.g., US, IN)">
            <mat-error *ngIf="codeControl?.hasError('required')">
              Country code is required
            </mat-error>
            <mat-error *ngIf="codeControl?.hasError('minlength')">
              Country code must be at least 2 characters
            </mat-error>
            <mat-error *ngIf="codeControl?.hasError('maxlength')">
              Country code cannot exceed 10 characters
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-slide-toggle formControlName="is_active" color="primary">
            {{ countryForm.get('is_active')?.value ? 'Active' : 'Inactive' }}
          </mat-slide-toggle>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions align="end">
      <button mat-button type="button" routerLink="..">Cancel</button>
      <button mat-raised-button color="primary" [disabled]="isLoading || countryForm.invalid" (click)="onSubmit()">
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </mat-card-actions>
  </mat-card>
</div>

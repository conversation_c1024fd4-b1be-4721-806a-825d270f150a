<!-- src/app/features/masters/supplier-type/supplier-type-view/supplier-type-view.component.html -->
<div class="container">
  <mat-card class="detail-card">
    <mat-card-header>
      <mat-card-title>Supplier Type Details</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" (click)="editSupplierType()" *ngIf="supplierType">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-raised-button [color]="supplierType.is_active ? 'accent' : 'primary'" (click)="toggleStatus()" *ngIf="supplierType">
          <mat-icon>{{ supplierType.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
          {{ supplierType.is_active ? 'Deactivate' : 'Activate' }}
        </button>
        <button mat-raised-button color="warn" (click)="deleteSupplierType()" *ngIf="supplierType">
          <mat-icon>delete</mat-icon> Delete
        </button>
        <button mat-raised-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon> Back
        </button>
      </div>
    </mat-card-header>

    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div class="error-container" *ngIf="errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="goBack()">Go Back</button>
    </div>

    <mat-card-content *ngIf="!isLoading && !errorMessage && supplierType">
      <div class="detail-section">
        <div class="status-chip">
          <mat-chip [color]="supplierType.is_active ? 'primary' : 'warn'" selected>
            {{ supplierType.is_active ? 'Active' : 'Inactive' }}
          </mat-chip>
        </div>

        <div class="detail-row">
          <div class="detail-label">ID:</div>
          <div class="detail-value">{{ supplierType.id }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Name:</div>
          <div class="detail-value">{{ supplierType.name }}</div>
        </div>

        <div class="detail-row" *ngIf="supplierType.description">
          <div class="detail-label">Description:</div>
          <div class="detail-value">{{ supplierType.description }}</div>
        </div>

        <mat-divider></mat-divider>

        <div class="detail-row">
          <div class="detail-label">Created By:</div>
          <div class="detail-value">{{ supplierType.created_by_username || 'N/A' }}</div>
        </div>

        <div class="detail-row" *ngIf="supplierType.created_at">
          <div class="detail-label">Created At:</div>
          <div class="detail-value">{{ supplierType.created_at | date:'medium' }}</div>
        </div>

        <div class="detail-row" *ngIf="supplierType.updated_at">
          <div class="detail-label">Last Updated:</div>
          <div class="detail-value">{{ supplierType.updated_at | date:'medium' }}</div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
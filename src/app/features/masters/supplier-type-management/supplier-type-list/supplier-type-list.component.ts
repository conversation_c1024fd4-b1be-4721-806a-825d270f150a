// src/app/features/masters/supplier-type/supplier-type-list/supplier-type-list.component.ts
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import {
  MatPaginatorModule,
  MatPaginator,
  PageEvent,
} from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';
import {
  SupplierTypeService,
  SupplierType,
} from '../../../../core/services/masters/supplier-type.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-supplier-type-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  templateUrl: './supplier-type-list.component.html',
  styleUrls: ['./supplier-type-list.component.scss'],
})
export class SupplierTypeListComponent implements OnInit {
  supplierTypes: SupplierType[] = [];
  filteredSupplierTypes: SupplierType[] = [];
  displayedSupplierTypes: SupplierType[] = [];
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'description',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<SupplierType>(true, []);
  isLoading = false;
  errorMessage = '';
  searchTerm = '';
  includeInactive = true; // Set to true to include inactive by default

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalSupplierTypes = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private supplierTypeService: SupplierTypeService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadSupplierTypes();
  }

  loadSupplierTypes(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.supplierTypeService
      .getAllSupplierTypes(this.includeInactive)
      .subscribe({
        next: (supplierTypes) => {
          this.supplierTypes = supplierTypes;
          this.totalSupplierTypes = supplierTypes.length;
          this.applyFilter();
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = `Failed to load supplier types: ${error.message}`;
          this.isLoading = false;
        },
      });
  }

  toggleIncludeInactive(): void {
    // Reload supplier types when the toggle changes
    this.loadSupplierTypes();
  }

  applyFilter(): void {
    const filterValue = this.searchTerm.toLowerCase();

    this.filteredSupplierTypes = this.supplierTypes.filter(
      (type) =>
        type.name.toLowerCase().includes(filterValue) ||
        (type.description &&
          type.description.toLowerCase().includes(filterValue)) ||
        (type.created_by_username &&
          type.created_by_username.toLowerCase().includes(filterValue))
    );

    this.totalSupplierTypes = this.filteredSupplierTypes.length;

    if (this.paginator) {
      this.paginator.firstPage();
    }

    this.updateDisplayedSupplierTypes();
  }

  updateDisplayedSupplierTypes(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedSupplierTypes = this.filteredSupplierTypes.slice(
      startIndex,
      endIndex
    );
  }

  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedSupplierTypes();
  }

  toggleStatus(supplierType: SupplierType): void {
    const newStatus = !supplierType.is_active;

    this.supplierTypeService
      .toggleSupplierTypeStatus(supplierType.id!, newStatus)
      .subscribe({
        next: (updatedType) => {
          supplierType.is_active = updatedType.is_active;
          this.showSnackBar(
            `Supplier Type ${supplierType.name} ${
              newStatus ? 'activated' : 'deactivated'
            } successfully`
          );
        },
        error: (error) => {
          this.showSnackBar(`Error: ${error.message}`, true);
          // Revert toggle in UI
          supplierType.is_active = !newStatus;
        },
      });
  }

  deleteSupplierType(supplierType: SupplierType): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the supplier type "${supplierType.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.supplierTypeService
          .deleteSupplierType(supplierType.id!)
          .subscribe({
            next: () => {
              this.supplierTypes = this.supplierTypes.filter(
                (c) => c.id !== supplierType.id
              );
              this.applyFilter();
              this.showSnackBar(
                `Supplier Type ${supplierType.name} deleted successfully`
              );
            },
            error: (error) => {
              this.showSnackBar(`Error: ${error.message}`, true);
            },
          });
      }
    });
  }

  bulkDeleteSelected(): void {
    const selectedSupplierTypes = this.selection.selected;

    if (selectedSupplierTypes.length === 0) {
      this.showSnackBar('No supplier types selected for deletion', true);
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${selectedSupplierTypes.length} selected supplier types?`,
        confirmText: 'Delete All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = selectedSupplierTypes.map((type) => type.id!);

        this.supplierTypeService.bulkDeleteSupplierTypes(ids).subscribe({
          next: () => {
            this.supplierTypes = this.supplierTypes.filter(
              (type) => !ids.includes(type.id!)
            );
            this.selection.clear();
            this.applyFilter();
            this.showSnackBar(
              `Successfully deleted ${ids.length} supplier types`
            );
          },
          error: (error) => {
            this.showSnackBar(`Error: ${error.message}`, true);
          },
        });
      }
    });
  }

  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedSupplierTypes.length;
    return numSelected === numRows && numRows > 0;
  }

  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedSupplierTypes);
    }
  }

  refreshList(): void {
    this.searchTerm = '';
    this.selection.clear();
    this.loadSupplierTypes();
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

<div class="designation-detail-container">
  <div class="detail-header">
    <h1>Designation Details</h1>
    <div class="header-actions">
      <button mat-raised-button color="primary" routerLink="../">
        <mat-icon>arrow_back</mat-icon> Back to List
      </button>
      <button mat-raised-button color="accent" [routerLink]="['../edit', designation.id]" *ngIf="designation">
        <mat-icon>edit</mat-icon> Edit
      </button>
    </div>
  </div>

  <mat-card>
    <mat-card-content>
      <!-- Loading spinner -->
      <div class="loading-spinner" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading designation details...</p>
      </div>

      <!-- Error message -->
      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon> {{ errorMessage }}
      </div>

      <!-- Designation details -->
      <div class="detail-content" *ngIf="designation && !isLoading">
        <div class="detail-header-section">
          <div class="detail-title">
            <h2>{{ designation.name }}</h2>
            <span class="detail-code">{{ designation.code }}</span>
          </div>
          <div class="detail-status">
            <span [ngClass]="getStatusClass()">{{ getStatusText() }}</span>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="detail-section">
          <h3>System Information</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <div class="detail-label">Created By</div>
              <div class="detail-value">{{ designation.created_by_username || 'N/A' }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Created At</div>
              <div class="detail-value">{{ getFormattedDate(designation.created_at) }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Updated By</div>
              <div class="detail-value">{{ designation.updated_by_username || 'N/A' }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Updated At</div>
              <div class="detail-value">{{ getFormattedDate(designation.updated_at) }}</div>
            </div>
          </div>
        </div>

        <div class="detail-actions">
          <button mat-raised-button color="primary" (click)="toggleStatus()" matTooltip="Change status">
            <mat-icon>{{ designation.is_active ? 'toggle_off' : 'toggle_on' }}</mat-icon>
            {{ designation.is_active ? 'Deactivate' : 'Activate' }}
          </button>
          <button mat-raised-button color="warn" (click)="deleteDesignation()" matTooltip="Delete this designation">
            <mat-icon>delete</mat-icon> Delete
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>

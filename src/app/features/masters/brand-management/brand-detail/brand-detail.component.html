<div class="container">
  <mat-card class="detail-card">
    <mat-card-header>
      <mat-card-title>Brand Details</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" [routerLink]="['../edit', brand.id]" matTooltip="Edit Brand" *ngIf="brand">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-raised-button color="accent" (click)="toggleBrandStatus()" matTooltip="{{ brand.is_active ? 'Deactivate' : 'Activate' }}" *ngIf="brand">
          <mat-icon>{{ brand.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
          {{ brand.is_active ? 'Deactivate' : 'Activate' }}
        </button>
        <button mat-raised-button color="warn" (click)="deleteBrand()" matTooltip="Delete Brand">
          <mat-icon>delete</mat-icon> Delete
        </button>
        <button mat-icon-button routerLink="../.." matTooltip="Back to List">
          <mat-icon>arrow_back</mat-icon>
        </button>
      </div>
    </mat-card-header>

    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div class="error-container" *ngIf="!isLoading && errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" routerLink="../..">
        <mat-icon>arrow_back</mat-icon> Back to List
      </button>
    </div>

    <mat-card-content *ngIf="!isLoading && !errorMessage && brand">
      <div class="detail-section">
        <div class="detail-row">
          <div class="detail-label">ID:</div>
          <div class="detail-value">{{ brand.id }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Name:</div>
          <div class="detail-value">{{ brand.name }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Product Subcategory:</div>
          <div class="detail-value">{{ brand.product_subcategory_name || 'N/A' }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Product Category:</div>
          <div class="detail-value">{{ brand.product_category_name || 'N/A' }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Status:</div>
          <div class="detail-value">
            <mat-chip [ngClass]="brand.is_active ? 'active-chip' : 'inactive-chip'">
              {{ brand.is_active ? 'Active' : 'Inactive' }}
            </mat-chip>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="detail-row">
          <div class="detail-label">Created By:</div>
          <div class="detail-value">{{ brand.created_by_username || 'N/A' }}</div>
        </div>

        <div class="detail-row" *ngIf="brand.created_at">
          <div class="detail-label">Created At:</div>
          <div class="detail-value">{{ brand.created_at | date:'medium' }}</div>
        </div>

        <div class="detail-row" *ngIf="brand.updated_by_username">
          <div class="detail-label">Updated By:</div>
          <div class="detail-value">{{ brand.updated_by_username }}</div>
        </div>

        <div class="detail-row" *ngIf="brand.updated_at">
          <div class="detail-label">Updated At:</div>
          <div class="detail-value">{{ brand.updated_at | date:'medium' }}</div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>

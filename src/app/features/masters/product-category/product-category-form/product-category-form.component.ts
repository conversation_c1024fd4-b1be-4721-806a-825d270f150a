import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import {
  ProductCategoryService,
  ProductCategory,
} from '../../../../core/services/masters/product-category.service';

@Component({
  selector: 'app-product-category-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    RouterModule,
  ],
  templateUrl: './product-category-form.component.html',
  styleUrl: './product-category-form.component.scss',
})
export class ProductCategoryFormComponent implements OnInit {
  productCategoryForm!: FormGroup;
  isLoading = false;
  isSubmitting = false;
  errorMessage = '';
  isEditMode = false;
  productCategoryId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private productCategoryService: ProductCategoryService
  ) {}

  ngOnInit(): void {
    this.initForm();

    // Check if we're in edit mode
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.productCategoryId = +id;
        this.isEditMode = true;
        this.loadProductCategory(this.productCategoryId);
      }
    });
  }

  initForm(): void {
    this.productCategoryForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', Validators.maxLength(255)],
      is_active: [true],
    });
  }

  loadProductCategory(id: number): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.productCategoryService.getProductCategoryById(id).subscribe({
      next: (productCategory) => {
        this.productCategoryForm.patchValue({
          name: productCategory.name,
          description: productCategory.description || '',
          is_active: productCategory.is_active,
        });
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load product category: ${error.message}`;
        this.isLoading = false;
      },
    });
  }

  onSubmit(): void {
    if (this.productCategoryForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    const productCategoryData: ProductCategory = {
      name: this.productCategoryForm.value.name,
      description: this.productCategoryForm.value.description,
      is_active: this.productCategoryForm.value.is_active,
    };

    if (this.isEditMode && this.productCategoryId) {
      this.updateProductCategory(this.productCategoryId, productCategoryData);
    } else {
      this.createProductCategory(productCategoryData);
    }
  }

  createProductCategory(productCategoryData: ProductCategory): void {
    this.productCategoryService
      .createProductCategory(productCategoryData)
      .subscribe({
        next: (createdProductCategory) => {
          this.isSubmitting = false;
          this.snackBar.open('Product category created successfully', 'Close', {
            duration: 3000,
          });
          this.router.navigate(['..'], { relativeTo: this.route });
        },
        error: (error) => {
          this.errorMessage = `Failed to create product category: ${error.message}`;
          this.isSubmitting = false;
        },
      });
  }

  updateProductCategory(
    id: number,
    productCategoryData: ProductCategory
  ): void {
    this.productCategoryService
      .updateProductCategory(id, productCategoryData)
      .subscribe({
        next: (updatedProductCategory) => {
          this.isSubmitting = false;
          this.snackBar.open('Product category updated successfully', 'Close', {
            duration: 3000,
          });
          this.router.navigate(['..'], { relativeTo: this.route });
        },
        error: (error) => {
          this.errorMessage = `Failed to update product category: ${error.message}`;
          this.isSubmitting = false;
        },
      });
  }

  cancel(): void {
    this.router.navigate(['..'], { relativeTo: this.route });
  }
}

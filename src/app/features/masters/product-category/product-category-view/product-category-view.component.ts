import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import {
  ProductCategoryService,
  ProductCategory,
} from '../../../../core/services/masters/product-category.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-product-category-view',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule,
    MatTooltipModule,
    MatSnackBarModule,
    MatDialogModule,
  ],
  templateUrl: './product-category-view.component.html',
  styleUrl: './product-category-view.component.scss',
})
export class ProductCategoryViewComponent implements OnInit {
  productCategory: ProductCategory | null = null;
  isLoading = false;
  errorMessage = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private productCategoryService: ProductCategoryService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadProductCategory();
  }

  loadProductCategory(): void {
    this.isLoading = true;
    this.errorMessage = '';

    const id = this.route.snapshot.paramMap.get('id');
    if (!id) {
      this.errorMessage = 'Product category ID is missing';
      this.isLoading = false;
      return;
    }

    this.productCategoryService.getProductCategoryById(+id).subscribe({
      next: (productCategory) => {
        this.productCategory = productCategory;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load product category: ${error.message}`;
        this.isLoading = false;
      },
    });
  }

  toggleProductCategoryStatus(): void {
    if (!this.productCategory) return;

    const newStatus = !this.productCategory.is_active;
    const id = this.productCategory.id!;

    this.productCategoryService
      .toggleProductCategoryStatus(id, newStatus)
      .subscribe({
        next: (updatedProductCategory) => {
          this.productCategory = updatedProductCategory;
          this.snackBar.open(
            `Product category ${updatedProductCategory.name} ${
              newStatus ? 'activated' : 'deactivated'
            } successfully`,
            'Close',
            { duration: 3000 }
          );
        },
        error: (error) => {
          this.snackBar.open(
            `Failed to update status: ${this.getErrorMessage(error)}`,
            'Close',
            { duration: 5000 }
          );
        },
      });
  }

  deleteProductCategory(): void {
    if (!this.productCategory) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the product category "${this.productCategory.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && this.productCategory) {
        this.productCategoryService
          .deleteProductCategory(this.productCategory.id!)
          .subscribe({
            next: (success) => {
              if (success) {
                this.snackBar.open(
                  `Product category ${
                    this.productCategory!.name
                  } deleted successfully`,
                  'Close',
                  { duration: 3000 }
                );
                this.router.navigate(['../..'], { relativeTo: this.route });
              }
            },
            error: (error) => {
              this.snackBar.open(
                `Failed to delete product category: ${this.getErrorMessage(
                  error
                )}`,
                'Close',
                { duration: 5000 }
              );
            },
          });
      }
    });
  }

  getErrorMessage(error: any): string {
    if (error.error && error.error.message) {
      return error.error.message;
    }
    return error.message || 'An unknown error occurred';
  }
}

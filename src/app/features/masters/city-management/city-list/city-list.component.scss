@import 'styles/variables';
@import 'styles/mixins';

.city-list-container {
  @include card-container;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: $text-color;
    }
  }

  mat-card {
    @include card-styling;
    background-color: $card-background;
  }

  .filter-container {
    margin-bottom: $spacing-lg;
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-md;

    .search-field {
      flex: 1;
      min-width: 250px;

      mat-form-field {
        width: 100%;
      }
    }

    .country-filter,
    .state-filter {
      width: 200px;

      mat-form-field {
        width: 100%;
      }
    }
  }

  .action-buttons {
    margin: $spacing-lg 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions, .right-actions {
      display: flex;
      align-items: center;
      gap: $spacing-md;
    }

    .show-inactive-toggle {
      margin-left: $spacing-md;
    }
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl 0;

    p {
      margin-top: $spacing-md;
      color: $text-secondary;
    }
  }

  .error-message {
    @include message-box($error-color);
  }

  .success-message {
    @include message-box($success-color);
  }

  .no-data-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl 0;
    color: $text-secondary;

    mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: $spacing-md;
      opacity: 0.5;
    }

    p {
      font-size: 16px;
    }
  }

  .table-container {
    @include table-container;
    background-color: $card-background;
    border-radius: $card-border-radius;
    overflow: hidden;

    .mat-column-select {
      width: 60px;
      padding-right: $spacing-sm;
    }

    .mat-column-id {
      width: 60px;
    }

    .mat-column-code {
      width: 80px;
    }

    .mat-column-state_name,
    .mat-column-country_name {
      width: 120px;
    }

    .mat-column-status {
      width: 100px;
    }

    .mat-column-actions {
      width: 160px;
      text-align: right;
    }
  }

  mat-paginator {
    margin-top: 0;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .city-list-container {
    padding: $spacing-md;

    .list-header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;
    }

    .filter-container {
      flex-direction: column;

      .search-field,
      .country-filter,
      .state-filter {
        width: 100%;
      }
    }

    .action-buttons {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;

      .left-actions {
        flex-wrap: wrap;
      }
    }

    .table-container {
      overflow-x: auto;
    }
  }
}

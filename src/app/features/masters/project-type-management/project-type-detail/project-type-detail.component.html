<div class="container">
  <mat-card class="detail-card">
    <mat-card-header>
      <mat-card-title>Project Type Details</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" (click)="editProjectType()" *ngIf="projectType">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-raised-button color="warn" (click)="deleteProjectType()" *ngIf="projectType">
          <mat-icon>delete</mat-icon> Delete
        </button>
        <button mat-raised-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon> Back
        </button>
      </div>
    </mat-card-header>
    
    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>
    
    <div class="error-container" *ngIf="errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="goBack()">Go Back</button>
    </div>
    
    <mat-card-content *ngIf="!isLoading && !errorMessage && projectType">
      <div class="detail-section">
        <div class="status-chip">
          <mat-chip [color]="projectType.is_active ? 'primary' : 'warn'" selected>
            {{ projectType.is_active ? 'Active' : 'Inactive' }}
          </mat-chip>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">ID:</div>
          <div class="detail-value">{{ projectType.id }}</div>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">Name:</div>
          <div class="detail-value">{{ projectType.name }}</div>
        </div>
        
        <div class="detail-row" *ngIf="projectType.description">
          <div class="detail-label">Description:</div>
          <div class="detail-value">{{ projectType.description }}</div>
        </div>
        
        <mat-divider></mat-divider>
        
        <div class="detail-row">
          <div class="detail-label">Created By:</div>
          <div class="detail-value">{{ projectType.created_by_username || 'N/A' }}</div>
        </div>
        
        <div class="detail-row" *ngIf="projectType.created_at">
          <div class="detail-label">Created At:</div>
          <div class="detail-value">{{ projectType.created_at | date:'medium' }}</div>
        </div>
        
        <div class="detail-row" *ngIf="projectType.updated_at">
          <div class="detail-label">Last Updated:</div>
          <div class="detail-value">{{ projectType.updated_at | date:'medium' }}</div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { EmploymentTypeService } from '../../../../core/services/masters/employment-type.service';
import { EmploymentType } from '../../../../core/models/masters/employment-type';

@Component({
  selector: 'app-employment-type-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    RouterModule,
  ],
  templateUrl: './employment-type-form.component.html',
  styleUrls: ['./employment-type-form.component.scss'],
})
export class EmploymentTypeFormComponent implements OnInit {
  employmentTypeForm!: FormGroup;
  isLoading = false;
  isSubmitting = false;
  errorMessage = '';
  isEditMode = false;
  employmentTypeId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private employmentTypeService: EmploymentTypeService
  ) {}

  ngOnInit(): void {
    this.initForm();

    // Check if we're in edit mode
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.employmentTypeId = +id;
        this.isEditMode = true;
        this.loadEmploymentType(this.employmentTypeId);
      }
    });
  }

  initForm(): void {
    this.employmentTypeForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      is_active: [true],
    });
  }

  loadEmploymentType(id: number): void {
    this.isLoading = true;
    this.employmentTypeService.getEmploymentTypeById(id).subscribe({
      next: (employmentType) => {
        this.employmentTypeForm.patchValue(employmentType);
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load employment type: ${error.message}`;
        this.showSnackBar(this.errorMessage, true);
        this.isLoading = false;
      },
    });
  }

  onSubmit(): void {
    if (this.employmentTypeForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    const formData = this.employmentTypeForm.value;

    if (this.isEditMode && this.employmentTypeId) {
      // Update existing employment type
      this.employmentTypeService
        .updateEmploymentType(this.employmentTypeId, formData)
        .subscribe({
          next: () => {
            this.isSubmitting = false;
            this.showSnackBar(`Employment Type updated successfully`);
            this.router.navigate(['../'], { relativeTo: this.route });
          },
          error: (error) => {
            this.isSubmitting = false;
            this.errorMessage = `Failed to update employment type: ${error.message}`;
            this.showSnackBar(this.errorMessage, true);
          },
        });
    } else {
      // Create new employment type
      this.employmentTypeService.createEmploymentType(formData).subscribe({
        next: () => {
          this.isSubmitting = false;
          this.showSnackBar(`Employment Type created successfully`);
          this.router.navigate(['../'], { relativeTo: this.route });
        },
        error: (error) => {
          this.isSubmitting = false;
          this.errorMessage = `Failed to create employment type: ${error.message}`;
          this.showSnackBar(this.errorMessage, true);
        },
      });
    }
  }

  onCancel(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

<div class="container">
  <mat-card class="detail-card">
    <mat-card-header>
      <mat-card-title>Employment Type Details</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" (click)="editEmploymentType()" *ngIf="employmentType">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-raised-button color="warn" (click)="deleteEmploymentType()" *ngIf="employmentType">
          <mat-icon>delete</mat-icon> Delete
        </button>
        <button mat-raised-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon> Back
        </button>
      </div>
    </mat-card-header>
    
    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>
    
    <div class="error-container" *ngIf="errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="goBack()">Go Back</button>
    </div>
    
    <mat-card-content *ngIf="!isLoading && !errorMessage && employmentType">
      <div class="detail-section">
        <div class="status-chip">
          <mat-chip [color]="employmentType.is_active ? 'primary' : 'warn'" selected>
            {{ employmentType.is_active ? 'Active' : 'Inactive' }}
          </mat-chip>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">ID:</div>
          <div class="detail-value">{{ employmentType.id }}</div>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">Name:</div>
          <div class="detail-value">{{ employmentType.name }}</div>
        </div>
        
        <mat-divider></mat-divider>
        
        <div class="detail-row">
          <div class="detail-label">Created By:</div>
          <div class="detail-value">{{ employmentType.created_by_username || 'N/A' }}</div>
        </div>
        
        <div class="detail-row" *ngIf="employmentType.created_at">
          <div class="detail-label">Created At:</div>
          <div class="detail-value">{{ employmentType.created_at | date:'medium' }}</div>
        </div>
        
        <div class="detail-row" *ngIf="employmentType.updated_at">
          <div class="detail-label">Last Updated:</div>
          <div class="detail-value">{{ employmentType.updated_at | date:'medium' }}</div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
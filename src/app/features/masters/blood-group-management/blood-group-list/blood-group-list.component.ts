import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule, MatTable } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';

import {
  BloodGroupService,
  BloodGroup,
} from '../../../../core/services/masters/blood-group.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-blood-group-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  templateUrl: './blood-group-list.component.html',
  styleUrls: ['./blood-group-list.component.scss'],
})
export class BloodGroupListComponent implements OnInit {
  bloodGroups: BloodGroup[] = [];
  filteredBloodGroups: BloodGroup[] = [];
  displayedBloodGroups: BloodGroup[] = []; // Blood groups after pagination
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<BloodGroup>(true, []); // Multiple selection model
  isLoading = true;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';
  includeInactive = false;

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalBloodGroups = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private bloodGroupService: BloodGroupService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadBloodGroups();
  }

  loadBloodGroups(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.bloodGroupService.getBloodGroups(this.includeInactive).subscribe({
      next: (response) => {
        if (response.success) {
          this.bloodGroups = Array.isArray(response.data) ? response.data : [];
          this.totalBloodGroups = this.bloodGroups.length;
          this.applyFilter();
        } else {
          this.errorMessage = response.message || 'Failed to load blood groups';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage =
          'Error loading blood groups: ' + this.getErrorMessage(error);
        this.isLoading = false;
        console.error('Error loading blood groups:', error);
      },
    });
  }

  applyFilter(): void {
    const filterValue = this.searchTerm.toLowerCase();

    this.filteredBloodGroups = this.bloodGroups.filter(
      (bloodGroup) =>
        bloodGroup.name.toLowerCase().includes(filterValue) ||
        (bloodGroup.created_by_username &&
          bloodGroup.created_by_username.toLowerCase().includes(filterValue))
    );

    this.totalBloodGroups = this.filteredBloodGroups.length;

    if (this.paginator) {
      this.paginator.firstPage();
    }

    this.updateDisplayedBloodGroups();
  }

  updateDisplayedBloodGroups(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedBloodGroups = this.filteredBloodGroups.slice(
      startIndex,
      endIndex
    );
  }

  onPageChange(event: any): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedBloodGroups();
  }

  toggleStatus(bloodGroup: BloodGroup): void {
    const newStatus = !bloodGroup.is_active;

    this.bloodGroupService
      .toggleBloodGroupStatus(bloodGroup.id, newStatus)
      .subscribe({
        next: (response) => {
          if (response.success) {
            bloodGroup.is_active = newStatus;
            this.showSnackBar(
              `Blood Group ${bloodGroup.name} ${
                newStatus ? 'activated' : 'deactivated'
              } successfully`
            );
          } else {
            this.showSnackBar(
              `Failed to update status: ${response.message}`,
              true
            );
          }
        },
        error: (error) => {
          this.showSnackBar(
            `Error updating status: ${this.getErrorMessage(error)}`,
            true
          );
          console.error('Error toggling blood group status:', error);
        },
      });
  }

  deleteBloodGroup(bloodGroup: BloodGroup): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the blood group "${bloodGroup.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.bloodGroupService.deleteBloodGroup(bloodGroup.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.bloodGroups = this.bloodGroups.filter(
                (bg) => bg.id !== bloodGroup.id
              );
              this.applyFilter();
              this.showSnackBar(
                `Blood Group ${bloodGroup.name} deleted successfully`
              );
            } else {
              this.showSnackBar(
                `Failed to delete blood group: ${response.message}`,
                true
              );
            }
          },
          error: (error) => {
            this.showSnackBar(
              `Error deleting blood group: ${this.getErrorMessage(error)}`,
              true
            );
            console.error('Error deleting blood group:', error);
          },
        });
      }
    });
  }

  bulkDeleteSelected(): void {
    const selectedBloodGroups = this.selection.selected;

    if (selectedBloodGroups.length === 0) {
      this.showSnackBar('No blood groups selected for deletion', true);
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${selectedBloodGroups.length} selected blood groups?`,
        confirmText: 'Delete All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = selectedBloodGroups.map((bloodGroup) => bloodGroup.id);

        this.bloodGroupService.bulkDeleteBloodGroups(ids).subscribe({
          next: (response) => {
            if (response.success) {
              this.bloodGroups = this.bloodGroups.filter(
                (bloodGroup) => !ids.includes(bloodGroup.id)
              );
              this.selection.clear();
              this.applyFilter();
              this.showSnackBar(
                `Successfully deleted ${ids.length} blood groups`
              );
            } else {
              this.showSnackBar(
                `Failed to delete blood groups: ${response.message}`,
                true
              );
            }
          },
          error: (error) => {
            this.showSnackBar(
              `Error deleting blood groups: ${this.getErrorMessage(error)}`,
              true
            );
            console.error('Error bulk deleting blood groups:', error);
          },
        });
      }
    });
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedBloodGroups.length;
    return numSelected === numRows && numRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedBloodGroups);
    }
  }

  refreshList(): void {
    this.loadBloodGroups();
  }

  toggleIncludeInactive(): void {
    this.includeInactive = !this.includeInactive;
    this.loadBloodGroups();
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error occurred';
  }
}

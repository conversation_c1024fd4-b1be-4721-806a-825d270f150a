.blood-group-list-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
  }

  mat-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 24px;
  }

  .filter-container {
    margin-bottom: 24px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .search-field {
      flex: 1;
      min-width: 250px;

      mat-form-field {
        width: 100%;
      }
    }
  }

  .action-buttons {
    margin: 24px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .show-inactive-toggle {
        margin-left: 16px;
      }
    }
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    p {
      margin-top: 16px;
      color: #666;
    }
  }

  .error-message {
    display: flex;
    align-items: center;
    padding: 16px;
    margin: 16px 0;
    border-radius: 4px;
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;

    mat-icon {
      margin-right: 8px;
    }
  }

  .success-message {
    display: flex;
    align-items: center;
    padding: 16px;
    margin: 16px 0;
    border-radius: 4px;
    background-color: rgba(76, 175, 80, 0.1);
    color: #4caf50;

    mat-icon {
      margin-right: 8px;
    }
  }

  .no-data-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #666;

    mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    p {
      font-size: 16px;
    }
  }

  .table-container {
    width: 100%;
    overflow: auto;

    table {
      width: 100%;

      th {
        font-weight: 500;
        color: #333;
      }

      td {
        color: #666;
      }

      th, td {
        padding: 8px 16px;
      }

      tr:hover {
        background-color: rgba(0, 0, 0, 0.02);
      }
    }
  }

  .status-active {
    color: #4caf50;
    font-weight: 500;
  }

  .status-inactive {
    color: #f44336;
    font-weight: 500;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .blood-group-list-container {
    padding: 16px;

    .list-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .filter-container {
      flex-direction: column;
    }

    .action-buttons {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .left-actions {
        flex-wrap: wrap;
      }
    }
    
    .table-container {
      overflow-x: auto;
    }
  }
}

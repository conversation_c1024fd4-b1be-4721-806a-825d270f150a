.blood-group-detail-container {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  mat-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 24px;
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    p {
      margin-top: 16px;
      color: #666;
    }
  }

  .error-message {
    display: flex;
    align-items: center;
    padding: 16px;
    margin: 16px 0;
    border-radius: 4px;
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;

    mat-icon {
      margin-right: 8px;
    }
  }

  .blood-group-info {
    .blood-group-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 500;
      }

      .status-chip {
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;

        &.active {
          background-color: rgba(76, 175, 80, 0.1);
          color: #4caf50;
        }

        &.inactive {
          background-color: rgba(244, 67, 54, 0.1);
          color: #f44336;
        }
      }
    }

    mat-divider {
      margin-bottom: 24px;
    }

    .detail-section {
      margin-bottom: 24px;
    }

    .detail-item {
      margin-bottom: 16px;
      display: flex;
      
      .label {
        font-weight: 500;
        min-width: 120px;
        color: #666;
      }
      
      .value {
        flex: 1;
      }
      
      .status-active {
        color: #4caf50;
        font-weight: 500;
      }
      
      .status-inactive {
        color: #f44336;
        font-weight: 500;
      }
    }

    .detail-actions {
      display: flex;
      gap: 16px;
      margin-top: 24px;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .blood-group-detail-container {
    padding: 16px;

    .detail-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;
        justify-content: space-between;
      }
    }

    .blood-group-info {
      .blood-group-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .detail-actions {
        flex-direction: column;
        width: 100%;

        button {
          width: 100%;
          margin-bottom: 8px;
        }
      }
    }
  }
}

.container {
  padding: 20px;
}

.detail-card {
  max-width: 800px;
  margin: 0 auto;
}

mat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.error-message {
  color: red;
  margin-bottom: 15px;
}

.detail-section {
  padding: 20px 0;
}

.detail-row {
  display: flex;
  margin-bottom: 15px;
}

.detail-label {
  font-weight: bold;
  width: 150px;
  color: #555;
}

.detail-value {
  flex: 1;
}

mat-divider {
  margin: 20px 0;
}

.active-chip {
  background-color: #4caf50;
  color: white;
}

.inactive-chip {
  background-color: #f44336;
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    align-items: flex-start;
  }

  .detail-row {
    flex-direction: column;
  }

  .detail-label {
    width: 100%;
    margin-bottom: 5px;
  }
}
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import {
  ProductSubcategoryService,
  ProductSubcategory,
} from '../../../../core/services/masters/product-subcategory.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-product-subcategory-view',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule,
    MatTooltipModule,
    MatSnackBarModule,
    MatDialogModule,
  ],
  templateUrl: './product-subcategory-view.component.html',
  styleUrl: './product-subcategory-view.component.scss',
})
export class ProductSubcategoryViewComponent implements OnInit {
  productSubcategory: ProductSubcategory | null = null;
  isLoading = false;
  errorMessage = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private productSubcategoryService: ProductSubcategoryService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadProductSubcategory();
  }

  loadProductSubcategory(): void {
    this.isLoading = true;
    this.errorMessage = '';

    const id = this.route.snapshot.paramMap.get('id');
    if (!id) {
      this.errorMessage = 'Product subcategory ID is missing';
      this.isLoading = false;
      return;
    }

    this.productSubcategoryService.getProductSubcategoryById(+id).subscribe({
      next: (productSubcategory) => {
        this.productSubcategory = productSubcategory;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load product subcategory: ${error.message}`;
        this.isLoading = false;
      },
    });
  }

  toggleProductSubcategoryStatus(): void {
    if (!this.productSubcategory) return;

    const newStatus = !this.productSubcategory.is_active;
    const id = this.productSubcategory.id!;

    this.productSubcategoryService
      .toggleProductSubcategoryStatus(id, newStatus)
      .subscribe({
        next: (updatedProductSubcategory) => {
          this.productSubcategory = updatedProductSubcategory;
          this.snackBar.open(
            `Product subcategory ${updatedProductSubcategory.name} ${
              newStatus ? 'activated' : 'deactivated'
            } successfully`,
            'Close',
            { duration: 3000 }
          );
        },
        error: (error) => {
          this.snackBar.open(
            `Failed to update status: ${this.getErrorMessage(error)}`,
            'Close',
            { duration: 5000 }
          );
        },
      });
  }

  deleteProductSubcategory(): void {
    if (!this.productSubcategory) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the product subcategory "${this.productSubcategory.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && this.productSubcategory) {
        this.productSubcategoryService
          .deleteProductSubcategory(this.productSubcategory.id!)
          .subscribe({
            next: (success) => {
              if (success) {
                this.snackBar.open(
                  `Product subcategory ${
                    this.productSubcategory!.name
                  } deleted successfully`,
                  'Close',
                  { duration: 3000 }
                );
                this.router.navigate(['../..'], { relativeTo: this.route });
              }
            },
            error: (error) => {
              this.snackBar.open(
                `Failed to delete product subcategory: ${this.getErrorMessage(
                  error
                )}`,
                'Close',
                { duration: 5000 }
              );
            },
          });
      }
    });
  }

  getErrorMessage(error: any): string {
    if (error.error && error.error.message) {
      return error.error.message;
    }
    return error.message || 'An unknown error occurred';
  }
}

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { QualificationService } from '../../../../core/services/masters/qualification.service';
import { Qualification } from '../../../../core/models/masters/qualification';

@Component({
  selector: 'app-qualification-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    RouterModule,
  ],
  templateUrl: './qualification-form.component.html',
  styleUrls: ['./qualification-form.component.scss'],
})
export class QualificationFormComponent implements OnInit {
  qualificationForm!: FormGroup;
  isLoading = false;
  isSubmitting = false;
  errorMessage = '';
  isEditMode = false;
  qualificationId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private qualificationService: QualificationService
  ) {}

  ngOnInit(): void {
    this.initForm();

    // Check if we're in edit mode
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.qualificationId = +id;
        this.isEditMode = true;
        this.loadQualification(this.qualificationId);
      }
    });
  }

  initForm(): void {
    this.qualificationForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      is_active: [true],
    });
  }

  loadQualification(id: number): void {
    this.isLoading = true;
    this.qualificationService.getQualificationById(id).subscribe({
      next: (qualification) => {
        this.qualificationForm.patchValue(qualification);
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load qualification: ${error.message}`;
        this.showSnackBar(this.errorMessage, true);
        this.isLoading = false;
      },
    });
  }

  onSubmit(): void {
    if (this.qualificationForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    const formData = this.qualificationForm.value;

    if (this.isEditMode && this.qualificationId) {
      // Update existing qualification
      this.qualificationService
        .updateQualification(this.qualificationId, formData)
        .subscribe({
          next: () => {
            this.isSubmitting = false;
            this.showSnackBar(`Qualification updated successfully`);
            this.router.navigate(['../../'], { relativeTo: this.route });
          },
          error: (error) => {
            this.isSubmitting = false;
            this.errorMessage = `Failed to update qualification: ${error.message}`;
            this.showSnackBar(this.errorMessage, true);
          },
        });
    } else {
      // Create new qualification
      this.qualificationService.createQualification(formData).subscribe({
        next: () => {
          this.isSubmitting = false;
          this.showSnackBar(`Qualification created successfully`);
          this.router.navigate(['../'], { relativeTo: this.route });
        },
        error: (error) => {
          this.isSubmitting = false;
          this.errorMessage = `Failed to create qualification: ${error.message}`;
          this.showSnackBar(this.errorMessage, true);
        },
      });
    }
  }

  onCancel(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

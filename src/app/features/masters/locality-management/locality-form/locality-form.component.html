<div class="locality-form-container">
  <div class="form-header">
    <h1>{{ pageTitle }}</h1>
    <button mat-raised-button color="primary" routerLink="../">
      <mat-icon>arrow_back</mat-icon> Back to List
    </button>
  </div>

  <mat-card>
    <mat-card-content>
      <!-- Loading spinner -->
      <div class="loading-spinner" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading locality data...</p>
      </div>

      <!-- Error message -->
      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon> {{ errorMessage }}
      </div>

      <!-- Locality form -->
      <form [formGroup]="localityForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading" #localityFormElement>
        <div class="form-row">
          <div class="form-field">
            <mat-form-field appearance="outline">
              <mat-label>City</mat-label>
              <mat-select formControlName="city_id">
                <mat-option>None</mat-option>
                <mat-option *ngFor="let city of cities" [value]="city.id">
                  {{ city.name }}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix *ngIf="loadingCities">sync</mat-icon>
              <mat-error *ngIf="cityIdControl?.invalid && cityIdControl?.touched">
                City is required
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div class="form-row">
          <div class="form-field">
            <mat-form-field appearance="outline">
              <mat-label>Locality Name</mat-label>
              <input matInput formControlName="name" placeholder="Enter locality name">
              <mat-error *ngIf="nameControl?.invalid && nameControl?.touched">
                <span *ngIf="nameControl?.errors?.['required']">Name is required</span>
                <span *ngIf="nameControl?.errors?.['maxlength']">Name cannot exceed 100 characters</span>
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div class="form-row">
          <div class="form-field status-toggle">
            <mat-slide-toggle formControlName="is_active" color="primary">
              Active
            </mat-slide-toggle>
            <div class="toggle-hint">
              Toggle to set the locality as active or inactive
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button mat-button type="button" routerLink="../" [disabled]="isSubmitting">
            Cancel
          </button>
          <button mat-raised-button color="primary" type="submit" [disabled]="isSubmitting" (click)="onButtonClick($event)">
            <mat-icon *ngIf="isSubmitting">
              <mat-spinner diameter="20" color="accent"></mat-spinner>
            </mat-icon>
            <span *ngIf="!isSubmitting">{{ submitButtonText }}</span>
            <span *ngIf="isSubmitting">Saving...</span>
          </button>

          <!-- Test button for direct API call -->
          <button mat-raised-button color="accent" type="button" [disabled]="isSubmitting" (click)="testDirectApiCall()">
            Test Direct API Call
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>

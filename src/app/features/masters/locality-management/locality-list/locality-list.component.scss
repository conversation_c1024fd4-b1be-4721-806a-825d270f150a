@import 'styles/variables';
@import 'styles/mixins';

.locality-list-container {
  @include card-container;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: $text-color;
    }
  }

  mat-card {
    @include card-styling;
    background-color: $card-background;
  }

  .filter-container {
    margin-bottom: $spacing-lg;
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-md;

    .search-field {
      flex: 1;
      min-width: 250px;

      mat-form-field {
        width: 100%;
      }
    }

    .country-filter,
    .state-filter,
    .city-filter {
      width: 200px;

      mat-form-field {
        width: 100%;
      }
    }
  }

  .action-buttons {
    margin: $spacing-lg 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions {
      display: flex;
      align-items: center;
      gap: $spacing-md;

      .show-inactive-toggle {
        margin-left: $spacing-md;
      }
    }
  }

  .loading-spinner {
    @include loading-spinner;
    flex-direction: column;
    text-align: center;

    p {
      margin-top: $spacing-sm;
      color: $text-light-color;
    }
  }

  .error-message {
    @include error-message;
  }

  .success-message {
    @include success-message;
  }

  .no-data-message {
    @include no-data-message;
  }

  .table-container {
    @include table-container;
    background-color: $card-background;
    border-radius: $card-border-radius;
    overflow: hidden;

    .mat-column-select {
      width: 60px;
      padding-right: $spacing-sm;
    }

    .mat-column-id {
      width: 60px;
    }

    .mat-column-code {
      width: 80px;
    }

    .mat-column-city_name,
    .mat-column-state_name,
    .mat-column-country_name {
      width: 120px;
    }

    .mat-column-status {
      width: 100px;
    }

    .mat-column-actions {
      width: 160px;
      text-align: right;
    }
  }

  mat-paginator {
    margin-top: 0;
  }

  .status-active {
    color: $success-color;
    font-weight: 500;
  }

  .status-inactive {
    color: $warning-color;
    font-weight: 500;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .locality-list-container {
    .filter-container {
      flex-direction: column;

      .search-field,
      .country-filter,
      .state-filter,
      .city-filter {
        width: 100%;
      }
    }

    .action-buttons {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;

      .left-actions {
        flex-wrap: wrap;
      }
    }

    .table-container {
      overflow-x: auto;
    }
  }
}

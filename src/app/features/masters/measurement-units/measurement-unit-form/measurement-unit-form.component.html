<div class="container">
  <mat-card class="form-card">
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit' : 'Create' }} Measurement Unit</mat-card-title>
    </mat-card-header>
    
    <div class="loading-shade" *ngIf="loading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>
    
    <div class="error-container" *ngIf="errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
    </div>
    
    <mat-card-content *ngIf="!loading">
      <form [formGroup]="measurementUnitForm" (ngSubmit)="onSubmit()">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Name</mat-label>
            <input matInput 
                   formControlName="name" 
                   placeholder="Enter measurement unit name (e.g., Meter, Kilogram)">
            <mat-error *ngIf="measurementUnitForm.get('name')?.hasError('required')">
              Name is required
            </mat-error>
            <mat-error *ngIf="measurementUnitForm.get('name')?.hasError('maxlength')">
              Name cannot exceed 100 characters
            </mat-error>
            <mat-error *ngIf="measurementUnitForm.get('name')?.hasError('pattern')">
              Name can only contain letters, numbers, spaces, hyphens, underscores, and parentheses
            </mat-error>
            <mat-hint>Enter the full name of the measurement unit</mat-hint>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Symbol</mat-label>
            <input matInput 
                   formControlName="symbol" 
                   placeholder="Enter symbol (e.g., m, kg, ft²)">
            <mat-error *ngIf="measurementUnitForm.get('symbol')?.hasError('required')">
              Symbol is required
            </mat-error>
            <mat-error *ngIf="measurementUnitForm.get('symbol')?.hasError('maxlength')">
              Symbol cannot exceed 20 characters
            </mat-error>
            <mat-error *ngIf="measurementUnitForm.get('symbol')?.hasError('pattern')">
              Symbol can only contain letters, numbers, superscripts (²³°), hyphens, underscores, and forward slashes
            </mat-error>
            <mat-hint>Enter the short symbol or abbreviation</mat-hint>
          </mat-form-field>
        </div>
        
        <div class="form-row status-toggle">
          <label class="status-label">Status</label>
          <mat-slide-toggle formControlName="is_active" color="primary">
            {{ measurementUnitForm.get('is_active')?.value ? 'Active' : 'Inactive' }}
          </mat-slide-toggle>
          <div class="status-hint">
            <mat-hint>Inactive measurement units will not be available for selection</mat-hint>
          </div>
        </div>
        
        <div class="form-actions">
          <button mat-raised-button type="button" (click)="onCancel()">Cancel</button>
          <button mat-raised-button color="primary" type="submit" [disabled]="measurementUnitForm.invalid || submitting">
            <mat-spinner diameter="20" *ngIf="submitting"></mat-spinner>
            <span *ngIf="!submitting">{{ isEditMode ? 'Update' : 'Create' }}</span>
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Help Section -->
  <mat-card class="help-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>help_outline</mat-icon>
        Examples & Guidelines
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <h6>Common Measurement Units:</h6>
      <div class="examples-grid">
        <div class="example-category">
          <h6>Length</h6>
          <ul class="help-list">
            <li><strong>Meter</strong> - m</li>
            <li><strong>Foot</strong> - ft</li>
            <li><strong>Inch</strong> - in</li>
            <li><strong>Yard</strong> - yd</li>
          </ul>
        </div>
        <div class="example-category">
          <h6>Area</h6>
          <ul class="help-list">
            <li><strong>Square Meter</strong> - m²</li>
            <li><strong>Square Foot</strong> - sq ft</li>
            <li><strong>Acre</strong> - acre</li>
            <li><strong>Hectare</strong> - ha</li>
          </ul>
        </div>
        <div class="example-category">
          <h6>Volume</h6>
          <ul class="help-list">
            <li><strong>Cubic Meter</strong> - m³</li>
            <li><strong>Liter</strong> - L</li>
            <li><strong>Gallon</strong> - gal</li>
            <li><strong>Cubic Foot</strong> - cu ft</li>
          </ul>
        </div>
        <div class="example-category">
          <h6>Weight</h6>
          <ul class="help-list">
            <li><strong>Kilogram</strong> - kg</li>
            <li><strong>Pound</strong> - lb</li>
            <li><strong>Ton</strong> - t</li>
            <li><strong>Gram</strong> - g</li>
          </ul>
        </div>
      </div>
      <mat-hint>Choose clear, standard names and symbols that are commonly understood in your industry.</mat-hint>
    </mat-card-content>
  </mat-card>
</div>

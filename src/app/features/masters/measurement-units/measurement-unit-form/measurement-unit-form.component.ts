import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  MeasurementUnitService,
  MeasurementUnit,
} from '../../../../core/services/measurement-unit.service';

@Component({
  selector: 'app-measurement-unit-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    MatSnackBarModule,
  ],
  templateUrl: './measurement-unit-form.component.html',
  styleUrls: ['./measurement-unit-form.component.scss'],
})
export class MeasurementUnitFormComponent implements OnInit {
  measurementUnitForm: FormGroup;
  isEditMode = false;
  measurementUnitId: number | null = null;
  loading = false;
  submitting = false;
  errorMessage = '';

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private measurementUnitService: MeasurementUnitService,
    private snackBar: MatSnackBar
  ) {
    this.measurementUnitForm = this.createForm();
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.isEditMode = true;
        this.measurementUnitId = +params['id'];
        this.loadMeasurementUnit();
      }
    });
  }

  createForm(): FormGroup {
    return this.fb.group({
      name: [
        '',
        [
          Validators.required,
          Validators.maxLength(100),
          Validators.pattern(/^[a-zA-Z0-9\s\-_()]+$/),
        ],
      ],
      symbol: [
        '',
        [
          Validators.required,
          Validators.maxLength(20),
          Validators.pattern(/^[a-zA-Z0-9²³°\-_/]+$/),
        ],
      ],
      is_active: [true],
    });
  }

  loadMeasurementUnit(): void {
    if (!this.measurementUnitId) return;

    this.loading = true;
    this.measurementUnitService.getMeasurementUnitById(this.measurementUnitId).subscribe({
      next: (response) => {
        if (response.success) {
          this.measurementUnitForm.patchValue({
            name: response.data.name,
            symbol: response.data.symbol,
            is_active: response.data.is_active,
          });
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading measurement unit:', error);
        this.errorMessage = 'Failed to load measurement unit';
        this.loading = false;
      },
    });
  }

  onSubmit(): void {
    if (this.measurementUnitForm.valid) {
      this.submitting = true;
      const formData = this.measurementUnitForm.value;

      const operation = this.isEditMode
        ? this.measurementUnitService.updateMeasurementUnit(this.measurementUnitId!, formData)
        : this.measurementUnitService.createMeasurementUnit(formData);

      operation.subscribe({
        next: (response) => {
          if (response.success) {
            this.showSnackBar(
              `Measurement unit ${this.isEditMode ? 'updated' : 'created'} successfully`
            );
            this.router.navigate(['/admin/masters/measurement-units']);
          }
          this.submitting = false;
        },
        error: (error) => {
          console.error('Error saving measurement unit:', error);
          let errorMessage = 'Failed to save measurement unit';
          
          if (error.error?.message) {
            errorMessage = error.error.message;
          }
          
          this.showSnackBar(errorMessage, true);
          this.submitting = false;
        },
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.router.navigate(['/admin/masters/measurement-units']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.measurementUnitForm.controls).forEach((key) => {
      const control = this.measurementUnitForm.get(key);
      control?.markAsTouched();
    });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

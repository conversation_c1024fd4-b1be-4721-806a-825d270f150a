import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import {
  MeasurementUnitService,
  MeasurementUnit,
} from '../../../../core/services/measurement-unit.service';

@Component({
  selector: 'app-measurement-unit-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatSlideToggleModule,
    MatPaginatorModule,
    MatSortModule,
  ],
  templateUrl: './measurement-unit-list.component.html',
  styleUrls: ['./measurement-unit-list.component.scss'],
})
export class MeasurementUnitListComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'symbol',
    'created_by',
    'status',
    'actions',
  ];
  displayedMeasurementUnits = new MatTableDataSource<MeasurementUnit>([]);
  selection = new SelectionModel<MeasurementUnit>(true, []);

  searchTerm = '';
  includeInactive = false;
  isLoading = false;
  errorMessage = '';

  // Pagination
  totalMeasurementUnits = 0;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];

  constructor(
    private measurementUnitService: MeasurementUnitService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadMeasurementUnits();
  }

  loadMeasurementUnits(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.measurementUnitService.getMeasurementUnits(this.includeInactive).subscribe({
      next: (response) => {
        if (response.success) {
          this.displayedMeasurementUnits.data = response.data;
          this.totalMeasurementUnits = response.data.length;
          this.applyFilter();
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading measurement units:', error);
        this.errorMessage = 'Failed to load measurement units';
        this.isLoading = false;
      },
    });
  }

  applyFilter(): void {
    const filterValue = this.searchTerm.trim().toLowerCase();
    this.displayedMeasurementUnits.filter = filterValue;

    if (this.displayedMeasurementUnits.paginator) {
      this.displayedMeasurementUnits.paginator.firstPage();
    }
  }

  toggleIncludeInactive(): void {
    this.loadMeasurementUnits();
  }

  refreshList(): void {
    this.selection.clear();
    this.loadMeasurementUnits();
  }

  // Selection methods
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedMeasurementUnits.data.length;
    return numSelected === numRows;
  }

  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
      return;
    }
    this.selection.select(...this.displayedMeasurementUnits.data);
  }

  // Action methods
  toggleStatus(measurementUnit: MeasurementUnit): void {
    const newStatus = !measurementUnit.is_active;
    this.measurementUnitService
      .toggleMeasurementUnitStatus(measurementUnit.id, newStatus)
      .subscribe({
        next: (response) => {
          if (response.success) {
            measurementUnit.is_active = newStatus;
            this.showSnackBar(
              `Measurement unit ${newStatus ? 'activated' : 'deactivated'} successfully`
            );
          }
        },
        error: (error) => {
          console.error('Error toggling measurement unit status:', error);
          this.showSnackBar('Failed to update measurement unit status', true);
        },
      });
  }

  deleteMeasurementUnit(measurementUnit: MeasurementUnit): void {
    if (
      confirm(`Are you sure you want to delete measurement unit "${measurementUnit.name}"?`)
    ) {
      this.measurementUnitService.deleteMeasurementUnit(measurementUnit.id).subscribe({
        next: (response) => {
          this.showSnackBar('Measurement unit deleted successfully');
          this.loadMeasurementUnits();
        },
        error: (error) => {
          console.error('Error deleting measurement unit:', error);
          this.showSnackBar('Failed to delete measurement unit', true);
        },
      });
    }
  }

  bulkDeleteSelected(): void {
    const selectedMeasurementUnits = this.selection.selected;
    if (selectedMeasurementUnits.length === 0) return;

    const confirmMessage = `Are you sure you want to delete ${selectedMeasurementUnits.length} measurement unit(s)?`;
    if (confirm(confirmMessage)) {
      const ids = selectedMeasurementUnits.map((mu) => mu.id);
      this.measurementUnitService.bulkDeleteMeasurementUnits(ids).subscribe({
        next: (response) => {
          this.showSnackBar(
            `${selectedMeasurementUnits.length} measurement unit(s) deleted successfully`
          );
          this.selection.clear();
          this.loadMeasurementUnits();
        },
        error: (error) => {
          console.error('Error in bulk delete:', error);
          this.showSnackBar('Failed to delete measurement units', true);
        },
      });
    }
  }

  // Pagination
  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    // Handle pagination if needed
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

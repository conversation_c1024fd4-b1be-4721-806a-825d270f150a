<div class="container">
  <mat-card class="form-card">
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit' : 'Create' }} GST Value</mat-card-title>
    </mat-card-header>

    <div class="loading-shade" *ngIf="loading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div class="error-container" *ngIf="errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
    </div>

    <mat-card-content *ngIf="!loading">
      <form [formGroup]="gstValueForm" (ngSubmit)="onSubmit()">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>GST Value (%)</mat-label>
            <input matInput
                   type="number"
                   formControlName="value"
                   placeholder="Enter GST value (e.g., 18)"
                   min="0"
                   max="100"
                   step="0.01">
            <mat-error *ngIf="gstValueForm.get('value')?.hasError('required')">
              GST Value is required
            </mat-error>
            <mat-error *ngIf="gstValueForm.get('value')?.hasError('min')">
              GST Value must be at least 0
            </mat-error>
            <mat-error *ngIf="gstValueForm.get('value')?.hasError('max')">
              GST Value must be at most 100
            </mat-error>
            <mat-error *ngIf="gstValueForm.get('value')?.hasError('pattern')">
              GST Value must be a valid number with up to 2 decimal places
            </mat-error>
            <mat-hint>Enter a value between 0 and 100 (up to 2 decimal places)</mat-hint>
          </mat-form-field>
        </div>

        <div class="form-row status-toggle">
          <label class="status-label">Status</label>
          <mat-slide-toggle formControlName="is_active" color="primary">
            {{ gstValueForm.get('is_active')?.value ? 'Active' : 'Inactive' }}
          </mat-slide-toggle>
          <div class="status-hint">
            <mat-hint>Inactive GST values will not be available for selection</mat-hint>
          </div>
        </div>

        <div class="form-actions">
          <button mat-raised-button type="button" (click)="onCancel()">Cancel</button>
          <button mat-raised-button color="primary" type="submit" [disabled]="gstValueForm.invalid || submitting">
            <mat-spinner diameter="20" *ngIf="submitting"></mat-spinner>
            <span *ngIf="!submitting">{{ isEditMode ? 'Update' : 'Create' }}</span>
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Help Section -->
  <mat-card class="help-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>help_outline</mat-icon>
        Help & Guidelines
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <h6>Common GST Rates in India:</h6>
      <ul class="help-list">
        <li><strong>0%</strong> - Essential items (basic food items, books, etc.)</li>
        <li><strong>5%</strong> - Basic necessities (sugar, tea, coffee, etc.)</li>
        <li><strong>12%</strong> - Standard rate (computers, processed food, etc.)</li>
        <li><strong>18%</strong> - Most goods and services</li>
        <li><strong>28%</strong> - Luxury items (cars, tobacco, etc.)</li>
      </ul>
      <mat-hint>You can enter custom GST rates as needed for your business requirements.</mat-hint>
    </mat-card-content>
  </mat-card>
</div>

<div class="container">
  <mat-card class="list-card">
    <mat-card-header>
      <mat-card-title>GST Values</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" routerLink="new">
          <mat-icon>add</mat-icon> Add GST Value
        </button>
        <button mat-raised-button color="warn" [disabled]="selection.isEmpty()" (click)="bulkDeleteSelected()">
          <mat-icon>delete</mat-icon> Delete Selected
        </button>
        <button mat-icon-button (click)="refreshList()" matTooltip="Refresh List">
          <mat-icon>refresh</mat-icon>
        </button>
      </div>
    </mat-card-header>

    <div class="filter-container">
      <mat-form-field appearance="outline">
        <mat-label>Search GST Values</mat-label>
        <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by value or creator">
        <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="searchTerm=''; applyFilter()">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-slide-toggle [(ngModel)]="includeInactive" (change)="toggleIncludeInactive()" color="primary">
        Include Inactive
      </mat-slide-toggle>
    </div>

    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div class="error-container" *ngIf="errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="refreshList()">Try Again</button>
    </div>

    <div class="table-container">
      <table mat-table [dataSource]="displayedGstValues" matSort>

        <!-- Checkbox Column -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox (change)="$event ? toggleAllRows() : null"
                         [checked]="selection.hasValue() && isAllSelected()"
                         [indeterminate]="selection.hasValue() && !isAllSelected()">
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let row">
            <mat-checkbox (click)="$event.stopPropagation()"
                         (change)="$event ? selection.toggle(row) : null"
                         [checked]="selection.isSelected(row)">
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
          <td mat-cell *matCellDef="let gstValue"> {{ gstValue.id }} </td>
        </ng-container>

        <!-- Value Column -->
        <ng-container matColumnDef="value">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> GST Value (%) </th>
          <td mat-cell *matCellDef="let gstValue">
            <strong>{{ gstValue.value }}%</strong>
          </td>
        </ng-container>

        <!-- Created By Column -->
        <ng-container matColumnDef="created_by">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
          <td mat-cell *matCellDef="let gstValue"> {{ gstValue.created_by_username || 'N/A' }} </td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
          <td mat-cell *matCellDef="let gstValue">
            <span [class]="gstValue.is_active ? 'active-status' : 'inactive-status'">
              {{ gstValue.is_active ? 'Active' : 'Inactive' }}
            </span>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef> Actions </th>
          <td mat-cell *matCellDef="let gstValue">
            <button mat-icon-button [routerLink]="[gstValue.id]" matTooltip="View Details">
              <mat-icon>visibility</mat-icon>
            </button>
            <button mat-icon-button [routerLink]="['edit', gstValue.id]" matTooltip="Edit">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button (click)="toggleStatus(gstValue)" matTooltip="{{ gstValue.is_active ? 'Deactivate' : 'Activate' }}">
              <mat-icon>{{ gstValue.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
            </button>
            <button mat-icon-button (click)="deleteGstValue(gstValue)" matTooltip="Delete">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

        <!-- Row shown when there is no matching data -->
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="6">No GST values found</td>
        </tr>
      </table>
    </div>

    <mat-paginator [length]="totalGstValues"
                  [pageSize]="pageSize"
                  [pageSizeOptions]="pageSizeOptions"
                  (page)="onPageChange($event)">
    </mat-paginator>
  </mat-card>
</div>

<div class="container">
  <mat-card class="detail-card">
    <mat-card-header>
      <mat-card-title>Project Status Details</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" (click)="editProjectStatus()" *ngIf="projectStatus">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-raised-button color="warn" (click)="deleteProjectStatus()" *ngIf="projectStatus">
          <mat-icon>delete</mat-icon> Delete
        </button>
        <button mat-raised-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon> Back
        </button>
      </div>
    </mat-card-header>
    
    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>
    
    <div class="error-container" *ngIf="errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="goBack()">Go Back</button>
    </div>
    
    <mat-card-content *ngIf="!isLoading && !errorMessage && projectStatus">
      <div class="detail-section">
        <div class="status-badge-container">
          <span class="status-badge" [ngClass]="{ 'active-status': projectStatus.is_active, 'inactive-status': !projectStatus.is_active }">
            {{ projectStatus.is_active ? 'Active' : 'Inactive' }}
          </span>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">ID:</div>
          <div class="detail-value">{{ projectStatus.id }}</div>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">Name:</div>
          <div class="detail-value">{{ projectStatus.name }}</div>
        </div>
        
        <mat-divider></mat-divider>
        
        <div class="detail-row">
          <div class="detail-label">Created By:</div>
          <div class="detail-value">{{ projectStatus.created_by_username || 'N/A' }}</div>
        </div>
        
        <div class="detail-row" *ngIf="projectStatus.created_at">
          <div class="detail-label">Created At:</div>
          <div class="detail-value">{{ projectStatus.created_at | date:'medium' }}</div>
        </div>
        
        <div class="detail-row" *ngIf="projectStatus.updated_at">
          <div class="detail-label">Last Updated:</div>
          <div class="detail-value">{{ projectStatus.updated_at | date:'medium' }}</div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
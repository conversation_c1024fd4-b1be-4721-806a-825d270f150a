.container {
  padding: 20px;
}

.detail-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

mat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.error-message {
  color: #f44336;
  margin-bottom: 15px;
}

.detail-section {
  padding: 10px;
}

/* Status badge styling */
.status-badge-container {
  margin-bottom: 20px;
}

.status-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
}

.active-status {
  background-color: #e6f4ea;
  color: #137333;
}

.inactive-status {
  background-color: #fce8e6;
  color: #c5221f;
}

.detail-row {
  display: flex;
  margin-bottom: 15px;
}

.detail-label {
  font-weight: 500;
  width: 150px;
  color: #555;
}

.detail-value {
  flex: 1;
}

mat-divider {
  margin: 20px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .detail-card {
    max-width: 100%;
  }
  
  .header-actions {
    flex-wrap: wrap;
  }
  
  .detail-row {
    flex-direction: column;
  }
  
  .detail-label {
    width: 100%;
    margin-bottom: 5px;
  }
}
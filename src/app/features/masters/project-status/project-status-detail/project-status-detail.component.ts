import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';
import {
  ProjectStatusService,
  ProjectStatus,
} from '../../../../core/services/masters/project-status.service';

@Component({
  selector: 'app-project-status-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatDividerModule,
    MatSnackBarModule,
    MatDialogModule,
  ],
  templateUrl: './project-status-detail.component.html',
  styleUrls: ['./project-status-detail.component.scss'],
})
export class ProjectStatusDetailComponent implements OnInit {
  projectStatus: ProjectStatus | null = null;
  isLoading = false;
  errorMessage = '';
  projectStatusId: number | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private projectStatusService: ProjectStatusService
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.projectStatusId = +id;
        this.loadProjectStatus(this.projectStatusId);
      } else {
        this.errorMessage = 'Project Status ID not provided';
        this.isLoading = false;
      }
    });
  }

  loadProjectStatus(id: number): void {
    this.isLoading = true;
    this.projectStatusService.getProjectStatusById(id).subscribe({
      next: (projectStatus) => {
        this.projectStatus = projectStatus;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load project status: ${error.message}`;
        this.isLoading = false;
      },
    });
  }

  editProjectStatus(): void {
    if (this.projectStatusId) {
      this.router.navigate(['../edit', this.projectStatusId], {
        relativeTo: this.route,
      });
    }
  }

  deleteProjectStatus(): void {
    if (!this.projectStatus) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the project status "${this.projectStatus.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && this.projectStatus) {
        this.projectStatusService
          .deleteProjectStatus(this.projectStatus.id!)
          .subscribe({
            next: () => {
              this.showSnackBar(
                `Project Status ${this.projectStatus?.name} deleted successfully`
              );
              this.router.navigate(['../../'], { relativeTo: this.route });
            },
            error: (error) => {
              this.showSnackBar(`Error: ${error.message}`, true);
            },
          });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

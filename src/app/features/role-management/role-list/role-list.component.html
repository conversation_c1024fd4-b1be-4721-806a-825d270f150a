<!-- src/app/features/role-management/role-list/role-list.component.html -->

<div class="role-list-container">
    <div class="role-list-header">
      <h1>Role Management</h1>
      <button mat-raised-button color="primary" [routerLink]="['/admin/roles/new']">
        <mat-icon>add</mat-icon> Add New Role
      </button>
    </div>
  
    <div class="filter-container">
      <mat-form-field appearance="outline">
        <mat-label>Search Roles</mat-label>
        <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name or description">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
    </div>
  
    <div class="mat-elevation-z2 table-container">
      <div *ngIf="isLoading" class="spinner-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading roles...</p>
      </div>
  
      <div *ngIf="!isLoading && errorMessage" class="error-container">
        <p>{{ errorMessage }}</p>
      </div>
  
      <table mat-table [dataSource]="displayedRoles" *ngIf="!isLoading && !errorMessage" class="role-table" matSort (matSortChange)="onSort($event)">
        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
          <td mat-cell *matCellDef="let role">{{ role.id }}</td>
        </ng-container>
  
        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
          <td mat-cell *matCellDef="let role">{{ role.name }}</td>
        </ng-container>
  
        <!-- Description Column -->
        <ng-container matColumnDef="description">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Description</th>
          <td mat-cell *matCellDef="let role">{{ role.description || 'N/A' }}</td>
        </ng-container>
  
        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let role">
            <mat-chip [color]="role.is_active ? 'accent' : 'warn'" selected>
              {{ role.is_active ? 'Active' : 'Inactive' }}
            </mat-chip>
          </td>
        </ng-container>
  
        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let role">
            <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Role actions">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #menu="matMenu">
              <a mat-menu-item [routerLink]="['/admin/roles', role.id]">
                <mat-icon>visibility</mat-icon>
                <span>View Details</span>
              </a>
              <a mat-menu-item [routerLink]="['/admin/roles/edit', role.id]">
                <mat-icon>edit</mat-icon>
                <span>Edit</span>
              </a>
              <button mat-menu-item (click)="toggleRoleStatus(role.id, role.is_active)">
                <mat-icon>{{ role.is_active ? 'block' : 'check_circle' }}</mat-icon>
                <span>{{ role.is_active ? 'Deactivate' : 'Activate' }}</span>
              </button>
              <button mat-menu-item (click)="deleteRole(role.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </td>
        </ng-container>
  
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
  
      <div *ngIf="!isLoading && !errorMessage && filteredRoles.length === 0" class="no-data-container">
        <p>No roles found.</p>
      </div>
  
      <!-- Paginator -->
      <mat-paginator *ngIf="!isLoading && !errorMessage && filteredRoles.length > 0"
                    [length]="totalRoles"
                    [pageSize]="pageSize"
                    [pageSizeOptions]="pageSizeOptions"
                    (page)="onPageChange($event)"
                    aria-label="Select page">
      </mat-paginator>
    </div>
  </div>
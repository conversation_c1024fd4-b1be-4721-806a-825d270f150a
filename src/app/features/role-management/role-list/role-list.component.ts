// src/app/features/role-management/role-list/role-list.component.ts

import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule, Sort } from '@angular/material/sort';
import {
  MatPaginatorModule,
  PageEvent,
  MatPaginator,
} from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatMenuModule } from '@angular/material/menu';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

import { Role } from '../../../core/models/user';
import { RoleService } from '../../../core/services/role.service';

@Component({
  selector: 'app-role-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatMenuModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
  ],
  templateUrl: './role-list.component.html',
  styleUrl: './role-list.component.scss',
})
export class RoleListComponent implements OnInit {
  roles: Role[] = [];
  filteredRoles: Role[] = [];
  displayedRoles: Role[] = []; // Roles after pagination
  displayedColumns: string[] = [
    'id',
    'name',
    'description',
    'status',
    'actions',
  ];
  isLoading = true;
  errorMessage = '';
  searchTerm = '';

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 5;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalRoles = 0;

  constructor(private roleService: RoleService) {}

  ngOnInit(): void {
    console.log('RoleListComponent initialized');
    this.loadRoles();
  }

  loadRoles(): void {
    console.log('Loading roles...');
    this.isLoading = true;
    this.roleService.getRoles(true).subscribe({
      next: (response) => {
        console.log('Roles loaded:', response);
        this.roles = response.data;
        this.applyFilter(); // This will handle filtering and display the first page
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.errorMessage = 'Error loading roles: ' + error.message;
        this.isLoading = false;
      },
    });
  }

  applyFilter(): void {
    const term = this.searchTerm.toLowerCase();
    this.filteredRoles = this.roles.filter(
      (role) =>
        role.name.toLowerCase().includes(term) ||
        (role.description && role.description.toLowerCase().includes(term))
    );

    this.totalRoles = this.filteredRoles.length;
    this.updateDisplayedRoles();
  }

  updateDisplayedRoles(): void {
    const startIndex = this.pageIndex * this.pageSize;
    this.displayedRoles = this.filteredRoles.slice(
      startIndex,
      startIndex + this.pageSize
    );
  }

  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedRoles();
  }

  onSort(sort: Sort): void {
    const data = [...this.filteredRoles];
    if (!sort.active || sort.direction === '') {
      this.filteredRoles = data;
      this.updateDisplayedRoles();
      return;
    }

    this.filteredRoles = data.sort((a, b) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'name':
          return this.compare(a.name, b.name, isAsc);
        case 'description':
          return this.compare(a.description || '', b.description || '', isAsc);
        case 'id':
          return this.compare(a.id, b.id, isAsc);
        default:
          return 0;
      }
    });

    this.updateDisplayedRoles();
  }

  compare(a: number | string, b: number | string, isAsc: boolean): number {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
  }

  deleteRole(roleId: number): void {
    if (confirm('Are you sure you want to delete this role?')) {
      this.roleService.deleteRole(roleId).subscribe({
        next: () => {
          this.roles = this.roles.filter((role) => role.id !== roleId);
          this.applyFilter(); // This will update filteredRoles and displayedRoles
        },
        error: (error) => {
          this.errorMessage = 'Error deleting role: ' + error.message;
        },
      });
    }
  }

  toggleRoleStatus(roleId: number, isActive: boolean): void {
    this.roleService.toggleRoleStatus(roleId, !isActive).subscribe({
      next: (response) => {
        // Update role in both arrays
        this.roles = this.roles.map((role) =>
          role.id === roleId ? { ...role, is_active: !isActive } : role
        );
        this.applyFilter(); // This will update filteredRoles and displayedRoles
      },
      error: (error) => {
        this.errorMessage = 'Error toggling role status: ' + error.message;
      },
    });
  }
}

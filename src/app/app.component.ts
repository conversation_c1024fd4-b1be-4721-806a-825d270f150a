import { Component, OnInit, OnDestroy } from '@angular/core';
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import { CommonModule } from '@angular/common';
import { filter, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component';
import { AuthLayoutComponent } from './layouts/auth-layout/auth-layout.component';
import { BrandingService } from './core/services/branding.service';
import { BrandingConfig } from './core/models/branding';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    AdminLayoutComponent,
    AuthLayoutComponent,
  ],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  isLoading = true;
  brandingError: string | null = null;

  constructor(
    private router: Router,
    private brandingService: BrandingService
  ) {
    this.initializeRouterEvents();
  }

  ngOnInit(): void {
    this.initializeBranding();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeRouterEvents(): void {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        // Scroll to top on route changes
        window.scrollTo(0, 0);
      });
  }

  private initializeBranding(): void {
    // Subscribe to branding changes
    this.brandingService.currentBranding$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (branding: BrandingConfig | null) => {
          if (branding) {
            this.applyBranding(branding);
            this.isLoading = false;
            this.brandingError = null;
          }
        },
        error: (error) => {
          console.warn('Branding initialization failed, using defaults:', error);
          this.brandingError = 'Failed to load branding configuration';
          this.isLoading = false;
          // Don't prevent app from loading on branding failure
        }
      });

    // Trigger initial branding load if not already loaded
    const currentBranding = this.brandingService.getCurrentBrandingValue();
    if (!currentBranding) {
      this.loadBranding();
    } else {
      this.isLoading = false;
    }
  }

  private loadBranding(): void {
    this.brandingService.getCurrentBranding()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (branding) => {
          this.applyBranding(branding);
          this.isLoading = false;
          this.brandingError = null;
        },
        error: (error) => {
          console.warn('Failed to load branding, continuing with defaults:', error);
          this.brandingError = 'Using default branding';
          this.isLoading = false;
          // Apply default branding to ensure app still works
          this.brandingService.applyBrandingToPage({
            appName: 'Plumeria Admin',
            companyName: 'Plumeria Construction',
            primaryColor: '#667eea',
            secondaryColor: '#764ba2',
            accentColor: '#4CAF50',
            errorColor: '#FF5722',
            backgroundColor: '#F8FAFC'
          } as BrandingConfig);
        }
      });
  }

  private applyBranding(branding: BrandingConfig): void {
    try {
      // Apply branding to page (title, CSS variables, etc.)
      this.brandingService.applyBrandingToPage(branding);
      
      // Additional app-level branding applications can go here
      this.updateDocumentMeta(branding);
      
    } catch (error) {
      console.error('Error applying branding:', error);
      this.brandingError = 'Error applying branding configuration';
    }
  }

  private updateDocumentMeta(branding: BrandingConfig): void {
    try {
      // Update meta description
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', 
          branding.tagline || `${branding.appName} - ${branding.companyName} Administration Panel`
        );
      } else {
        // Create meta description if it doesn't exist
        const meta = document.createElement('meta');
        meta.name = 'description';
        meta.content = branding.tagline || `${branding.appName} - ${branding.companyName} Administration Panel`;
        document.getElementsByTagName('head')[0].appendChild(meta);
      }

      // Update meta keywords
      const metaKeywords = document.querySelector('meta[name="keywords"]');
      const keywords = `${branding.appName}, ${branding.companyName}, admin, dashboard, management`;
      if (metaKeywords) {
        metaKeywords.setAttribute('content', keywords);
      } else {
        const meta = document.createElement('meta');
        meta.name = 'keywords';
        meta.content = keywords;
        document.getElementsByTagName('head')[0].appendChild(meta);
      }
    } catch (error) {
      console.warn('Error updating document meta:', error);
    }
  }
}

// Local variables for header component
$header-height: 64px;
$primary-color: #3498db;
$text-color: #333;
$text-lighter-color: #999999;
$border-color: #e0e0e0;
$success-color: #2ecc71;
$warn-color: #e74c3c;
$info-color: #3498db;

.header {
  height: $header-height;
  background-color: white;
  border-bottom: 1px solid $border-color;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  position: relative;
  z-index: 100;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
}

.menu-toggle {
  margin-right: 16px;
}

.title-section {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.page-title {
  font-size: 18px;
  font-weight: 500;
  color: $text-color;
}

.company-name {
  font-size: 11px;
  color: $text-lighter-color;
  padding: 2px 8px;
  background-color: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.action-button {
  margin-left: 8px;
}

.user-button {
  margin-left: 16px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: $primary-color;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  
  &.large {
    width: 48px;
    height: 48px;
    font-size: 18px;
  }
}

.menu-header {
  padding: 16px;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
  
  &.user-header {
    display: flex;
    align-items: center;
    
    .user-details {
      margin-left: 16px;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
      
      p {
        margin: 4px 0 0;
        font-size: 12px;
        color: #666;
      }
      
      .company-tag {
        display: inline-block;
        font-size: 10px;
        color: $primary-color;
        background-color: rgba(52, 152, 219, 0.1);
        padding: 2px 6px;
        border-radius: 8px;
        margin-top: 4px;
        font-weight: 500;
      }
    }
  }
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
  min-width: 280px;
}

.notification-item {
  display: flex !important;
  align-items: center;
  padding: 8px 16px;
  
  .notification-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    
    &.system {
      background-color: $info-color;
      color: white;
    }
    
    &.user {
      background-color: $success-color;
      color: white;
    }
    
    &.alert {
      background-color: $warn-color;
      color: white;
    }
    
    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
  
  .notification-content {
    flex: 1;
    
    .notification-text {
      margin: 0;
      font-size: 14px;
    }
    
    .notification-time {
      margin: 4px 0 0;
      font-size: 12px;
      color: #888;
    }
  }
}

.no-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 16px;
  color: #888;
  
  mat-icon {
    font-size: 32px;
    height: 32px;
    width: 32px;
    margin-bottom: 8px;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

.menu-footer {
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  border-top: 1px solid $border-color;
}

// This ensures that the menus are wider and have better spacing
::ng-deep {
  .notification-menu {
    max-width: none !important;
    
    .mat-mdc-menu-content {
      padding: 0 !important;
    }
  }
  
  .user-menu {
    min-width: 250px !important;
    
    .mat-mdc-menu-content {
      padding: 0 !important;
    }
  }
}
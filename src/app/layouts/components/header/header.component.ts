import { Component, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterLink, NavigationEnd } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatBadgeModule } from '@angular/material/badge';
import { AuthService } from '../../../core/services/auth.service';
import { MatDividerModule } from '@angular/material/divider';
import { BrandingService } from '../../../core/services/branding.service';
import { BrandingConfig } from '../../../core/models/branding';
import { Subject, Observable } from 'rxjs';
import { takeUntil, filter, map, startWith } from 'rxjs/operators';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatBadgeModule,
    MatDividerModule,
  ],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit, OnDestroy {
  @Output() toggleSidenav = new EventEmitter<void>();
  
  private destroy$ = new Subject<void>();
  
  // Branding observables
  branding$ = this.brandingService.currentBranding$;
  currentPageTitle$: Observable<string>;

  // Route title mapping
  private readonly routeTitles: { [key: string]: string } = {
    '/admin': 'Dashboard',
    '/admin/dashboard': 'Dashboard',
    '/admin/users': 'User Management',
    '/admin/roles': 'Role Management',
    '/admin/permissions': 'Permission Management',
    '/admin/modules': 'Module Management',
    '/admin/staff': 'Staff Management',
    '/admin/branding': 'Branding Management',
    '/admin/profile': 'My Profile',
    '/admin/settings': 'Settings',
  };

  notifications = [
    {
      type: 'system',
      icon: 'info',
      text: 'System maintenance scheduled',
      time: '20 minutes ago',
    },
    {
      type: 'user',
      icon: 'person',
      text: 'New user registered',
      time: '1 hour ago',
    },
    {
      type: 'alert',
      icon: 'warning',
      text: 'Login attempt from unknown device',
      time: '2 hours ago',
    },
    {
      type: 'system',
      icon: 'update',
      text: 'System updated to version 2.1.0',
      time: '1 day ago',
    },
  ];

  constructor(
    public authService: AuthService, 
    private router: Router,
    private brandingService: BrandingService
  ) {
    // Set up page title observable
    this.currentPageTitle$ = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map(() => this.getPageTitle()),
      startWith(this.getPageTitle())
    );
  }

  ngOnInit(): void {
    // No additional initialization needed - observables are set up in constructor
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onToggleSidenav(): void {
    this.toggleSidenav.emit();
  }

  navigateToProfile(): void {
    this.router.navigate(['/admin/profile']);
  }

  navigateToSettings(): void {
    this.router.navigate(['/admin/settings']);
  }

  markAllAsRead(): void {
    // Implement mark all as read functionality
    console.log('Marked all notifications as read');
  }

  logout(): void {
    this.authService.logout();
  }

  getInitials(): string {
    const user = this.authService.currentUserValue;
    if (user && user.name) {
      return user.name.charAt(0).toUpperCase();
    } else if (user && user.username) {
      return user.username.charAt(0).toUpperCase();
    }
    return 'A'; // Default fallback
  }

  getNotificationCount(): number {
    return this.notifications.length;
  }

  getPageTitle(): string {
    const currentUrl = this.router.url;
    return this.routeTitles[currentUrl] || 'Admin Panel';
  }

  getAppName(): string {
    const branding = this.brandingService.getCurrentBrandingValue();
    return branding?.appName || 'Admin Panel';
  }

  getCompanyName(): string {
    const branding = this.brandingService.getCurrentBrandingValue();
    return branding?.companyName || 'Company';
  }

  getBrandingOrDefault(): BrandingConfig {
    const branding = this.brandingService.getCurrentBrandingValue();
    return branding || {
      appName: 'Plumeria Admin',
      companyName: 'Plumeria Construction',
      primaryColor: '#667eea',
      secondaryColor: '#764ba2',
      accentColor: '#4CAF50',
      errorColor: '#FF5722',
      backgroundColor: '#F8FAFC'
    } as BrandingConfig;
  }
}

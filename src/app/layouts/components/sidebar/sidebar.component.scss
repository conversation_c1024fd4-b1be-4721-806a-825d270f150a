$sidebar-bg: #2c3e50;
$sidebar-width: 280px;
$sidebar-collapsed-width: 70px;
$text-color: #ecf0f1;
$active-color: #3498db;
$hover-bg: rgba(255, 255, 255, 0.1);
$submenu-bg: rgba(0, 0, 0, 0.15);
$border-color: rgba(255, 255, 255, 0.07);
$add-button-color: rgba(255, 255, 255, 0.6);
$add-button-hover-color: #fff;
$add-button-active-color: $active-color;

.sidebar {
  background-color: $sidebar-bg;
  height: 100%;
  width: $sidebar-width;
  color: $text-color;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 100;
  overflow: visible;

  &.collapsed {
    width: $sidebar-collapsed-width;
  }
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
  }
}

.user-profile {
  padding: 20px 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid $border-color;
  background-color: rgba(0, 0, 0, 0.15);

  .avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background-color: $active-color;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

    mat-icon {
      font-size: 24px;
      height: 24px;
      width: 24px;
      color: white;
    }
  }

  .user-info {
    flex: 1;
    min-width: 0;
    overflow: hidden;

    h3 {
      margin: 0;
      font-size: 15px;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      letter-spacing: 0.3px;
    }

    p {
      margin: 4px 0 0;
      font-size: 12px;
      opacity: 0.7;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.user-collapsed {
  padding: 16px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid $border-color;
  background-color: rgba(0, 0, 0, 0.15);

  .avatar-small {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background-color: $active-color;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

    mat-icon {
      font-size: 24px;
      height: 24px;
      width: 24px;
      color: white;
    }
  }
}

.menu-container {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
}

.menu-divider {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.07);
  margin: 4px 16px;

  // Hide divider if it's the only visible element or if there's nothing after it
  &:last-child {
    display: none;
  }
}

// Container for menu item with add button
.menu-item-container, .submenu-item-container {
  position: relative;
  display: flex;
  align-items: center;
  margin: 2px 8px;
  border-radius: 4px;
  overflow: hidden;

  // The main menu item takes most of the space
  .menu-item, .submenu-item {
    flex: 1;
    text-decoration: none;
    color: $text-color;
  }

  // Style for the add button
  .add-button {
    width: 32px;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    margin-left: 4px;
    color: $add-button-color;
    text-decoration: none;
    transition: all 0.2s ease;

    &:hover {
      background-color: $hover-bg;
      color: $add-button-hover-color;
    }

    &.active-add-button {
      color: $add-button-active-color;
      background-color: rgba($active-color, 0.15);
    }

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }

  .submenu-add-button {
    width: 28px;
    min-width: 28px;
    height: 28px;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

.menu-item {
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px 0 16px;
  text-decoration: none;
  color: $text-color;
  position: relative;
  cursor: pointer;
  border-radius: 4px;
  user-select: none;
  transition: all 0.2s ease;

  .left-content {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    overflow: hidden;
  }

  &:hover {
    background-color: $hover-bg;
  }

  .menu-icon {
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 12px;
    flex-shrink: 0;
    position: relative;

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      transition: color 0.2s ease;
    }
  }

  .menu-label {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    letter-spacing: 0.2px;
    max-width: 160px;
    font-weight: 400;
    transition: color 0.2s ease;
  }

  .expand-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    margin-left: 4px;
    opacity: 0.7;
    flex-shrink: 0;
    transition: transform 0.3s ease;
  }

  &.parent-item.active {
    background-color: rgba($active-color, 0.15);

    .menu-icon mat-icon {
      color: $active-color;
    }

    .menu-label {
      color: white;
      font-weight: 500;
    }
  }

  .active-indicator {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    border-radius: 0 3px 3px 0;
    background-color: transparent;
    transition: background-color 0.2s ease;
  }

  &.active-link {
    background-color: rgba($active-color, 0.2);

    .menu-icon mat-icon {
      color: $active-color;
    }

    .menu-label {
      color: white;
      font-weight: 500;
    }

    .active-indicator {
      background-color: $active-color;
      box-shadow: 0 0 8px rgba($active-color, 0.5);
    }
  }
}

.menu-group {
  display: flex;
  flex-direction: column;
  overflow: visible;
  position: relative;

  // Hide empty menu groups
  &:empty {
    display: none;
  }
}

.submenu {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: 0 0px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  position: relative;

  &.collapsed-menu {
    position: absolute;
    left: $sidebar-collapsed-width;
    top: 0;
    min-width: 200px;
    z-index: 1000;
    background-color: darken($sidebar-bg, 10%);
    box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.3);
    border-radius: 0 4px 4px 0;
    margin: 0;
    padding: 4px;
    overflow: visible;

    .submenu-item {
      padding-left: 16px;
    }
  }

  .submenu-item {
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: start;
    text-decoration: none;
    color: $text-color;
    position: relative;
    padding: 0 10px 0 12px;
    border-radius: 4px;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;

    .left-content {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;
      overflow: hidden;
    }

    &:hover {
      background-color: $hover-bg;
    }

    .menu-icon {
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 8px;
      flex-shrink: 0;
      position: relative;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
        transition: color 0.2s ease;
      }
    }

    .menu-label {
      font-size: 13px;
    }

    .active-indicator {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      border-radius: 0 3px 3px 0;
      background-color: transparent;
      transition: background-color 0.2s ease;
    }

    &.active-sublink {
      background-color: rgba($active-color, 0.2);

      .menu-icon mat-icon {
        color: $active-color;
      }

      .menu-label {
        color: white;
        font-weight: 500;
      }

      .active-indicator {
        background-color: $active-color;
        box-shadow: 0 0 8px rgba($active-color, 0.5);
      }
    }
  }
}

.sidebar-footer {
  border-top: 1px solid $border-color;
  padding: 8px 0;
  background-color: rgba(0, 0, 0, 0.15);

  .menu-item {
    opacity: 0.8;
    justify-content: flex-start;

    &:hover {
      opacity: 1;
    }
  }
}

// Badge styles
::ng-deep {
  .mat-badge-content {
    font-size: 10px !important;
    height: 16px !important;
    width: 16px !important;
    line-height: 16px !important;
  }

  // Position badge better for menu icons
  .mat-badge-small .mat-badge-content {
    right: -5px !important;
    top: -5px !important;
  }

  // Add animation to badge
  .mat-badge-content {
    transition: all 0.3s ease;

    &.mat-badge-active {
      transform: scale(1.1);
    }
  }
}

// Material ripple overrides
::ng-deep {
  .mat-ripple-element {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
}
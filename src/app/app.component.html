<!-- Branding initialization loading state -->
<div *ngIf="isLoading" class="branding-loading">
  <div class="loading-spinner">
    <div class="spinner"></div>
    <p>Loading application...</p>
  </div>
</div>

<!-- Show branding error if any (non-blocking) -->
<div *ngIf="brandingError && !isLoading" class="branding-error">
  <small>{{ brandingError }}</small>
</div>

<!-- Main application content -->
<div [class.branding-loaded]="!isLoading">
  <router-outlet></router-outlet>
</div>
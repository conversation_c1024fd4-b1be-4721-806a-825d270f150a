import { Routes } from '@angular/router';
import { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component';
import { AuthLayoutComponent } from './layouts/auth-layout/auth-layout.component';
import { LoginComponent } from './features/login/login.component';
import { DashboardComponent } from './features/dashboard/dashboard.component';
import { UserManagementComponent } from './features/user-management/user-management.component';
import { RoleManagementComponent } from './features/role-management/role-management.component';
import { ModuleManagementComponent } from './features/module-management/module-management.component';
import { UserListComponent } from './features/user-management/user-list/user-list.component';
import { UserFormComponent } from './features/user-management/user-form/user-form.component';
import { UserDetailComponent } from './features/user-management/user-detail/user-detail.component';
import { RoleListComponent } from './features/role-management/role-list/role-list.component';
import { RoleFormComponent } from './features/role-management/role-form/role-form.component';
import { RoleDetailComponent } from './features/role-management/role-detail/role-detail.component';
import { ModuleListComponent } from './features/module-management/module-list/module-list.component';
import { ModuleFormComponent } from './features/module-management/module-form/module-form.component';
import { ModuleDetailComponent } from './features/module-management/module-detail/module-detail.component';
import { PermissionManagementComponent } from './features/permission-management/permission-management.component';
import { ProfileComponent } from './features/profile/profile.component';
import { AuthGuard } from './core/guards/auth.guard';
import { PermissionGuard } from './core/guards/permission.guard';
import { ForbiddenComponent } from './shared/components/forbidden.component';
import { MASTERS_ROUTES } from './features/masters/masters.routes';
import { STAFF_MANAGEMENT_ROUTES } from './features/staff-management/staff-management.routes';
import { BrandingManagementComponent } from './features/branding-management/branding-management.component';
export const routes: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
  {
    path: '',
    component: AuthLayoutComponent,
    children: [
      { path: 'login', component: LoginComponent },
      { path: 'forbidden', component: ForbiddenComponent },
      // Add more auth routes as needed
    ],
  },
  {
    path: 'admin',
    component: AdminLayoutComponent,
    canActivate: [AuthGuard], // Activate the auth guard
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: DashboardComponent },
      { path: 'profile', component: ProfileComponent }, // Add profile route
      {
        path: 'modules',
        component: ModuleManagementComponent,
        canActivate: [PermissionGuard],
        data: { module: 'module', action: 'read' },
        children: [
          { path: '', component: ModuleListComponent },
          {
            path: 'new',
            component: ModuleFormComponent,
            canActivate: [PermissionGuard],
            data: { module: 'module', action: 'create' },
          },
          { path: ':id', component: ModuleDetailComponent },
          {
            path: 'edit/:id',
            component: ModuleFormComponent,
            canActivate: [PermissionGuard],
            data: { module: 'module', action: 'update' },
          },
        ],
      },
      {
        path: 'users',
        component: UserManagementComponent,
        canActivate: [PermissionGuard],
        data: { module: 'user', action: 'read' },
        children: [
          { path: '', component: UserListComponent },
          {
            path: 'new',
            component: UserFormComponent,
            canActivate: [PermissionGuard],
            data: { module: 'user', action: 'create' },
          },
          { path: ':id', component: UserDetailComponent },
          {
            path: 'edit/:id',
            component: UserFormComponent,
            canActivate: [PermissionGuard],
            data: { module: 'user', action: 'update' },
          },
        ],
      },

      {
        path: 'roles',
        component: RoleManagementComponent,
        canActivate: [PermissionGuard],
        data: { module: 'role', action: 'read' },
        children: [
          { path: '', component: RoleListComponent },
          {
            path: 'new',
            component: RoleFormComponent,
            canActivate: [PermissionGuard],
            data: { module: 'role', action: 'create' },
          },
          { path: ':id', component: RoleDetailComponent },
          {
            path: 'edit/:id',
            component: RoleFormComponent,
            canActivate: [PermissionGuard],
            data: { module: 'role', action: 'update' },
          },
        ],
      },
      {
        path: 'permissions',
        component: PermissionManagementComponent,
        canActivate: [PermissionGuard],
        data: { module: 'permission', action: 'read' },
      },
      {
        path: 'masters',
        canActivate: [AuthGuard],
        children: MASTERS_ROUTES,
      },
      {
        path: 'staff-management',
        canActivate: [AuthGuard],
        children: STAFF_MANAGEMENT_ROUTES,
      },
      {
        path: 'branding',
        component: BrandingManagementComponent,
        canActivate: [AuthGuard],
      },
    ],
  },
  {
    path: '**',
    redirectTo: 'login',
  },
];

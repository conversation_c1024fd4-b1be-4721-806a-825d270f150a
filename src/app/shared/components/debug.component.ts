// src/app/shared/components/debug.component.ts
import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../core/services/auth.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-debug',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="debug-panel">
      <h3>Auth Debug Information</h3>
      <div>
        <strong>Is Authenticated:</strong> {{ isAuthenticated }}
      </div>
      <div>
        <strong>Current User:</strong> {{ currentUserJson }}
      </div>
      <div>
        <strong>Has Admin Role:</strong> {{ hasAdminRole }}
      </div>
      <div>
        <strong>User Roles:</strong> {{ userRolesJson }}
      </div>
      <div>
        <strong>Access Token:</strong> {{ accessToken ? 'Present' : 'Not Present' }}
      </div>
      <div>
        <strong>Token Data:</strong> {{ tokenData<PERSON>son }}
      </div>
      <button (click)="refreshDebugInfo()">Refresh Debug Info</button>
    </div>
  `,
  styles: [`
    .debug-panel {
      background-color: #f8f9fa;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin: 15px 0;
      font-family: monospace;
    }
    button {
      margin-top: 10px;
      padding: 5px 10px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #0069d9;
    }
  `]
})
export class DebugComponent implements OnInit {
  isAuthenticated = false;
  currentUserJson = '';
  hasAdminRole = false;
  userRolesJson = '';
  accessToken: string | null = null;
  tokenDataJson = '';

  constructor(private authService: AuthService) {}

  ngOnInit() {
    this.refreshDebugInfo();
  }

  refreshDebugInfo() {
    // Check authentication status
    this.isAuthenticated = this.authService.isAuthenticated();
    
    // Get current user
    const currentUser = this.authService.currentUserValue;
    this.currentUserJson = JSON.stringify(currentUser, null, 2);
    
    // Check admin role
    this.hasAdminRole = this.authService.hasRole('admin');
    
    // Get user roles
    const userRoles = currentUser?.roles || [];
    this.userRolesJson = JSON.stringify(userRoles, null, 2);
    
    // Get token
    this.accessToken = this.authService.getToken();
    
    // Decode token
    if (this.accessToken) {
      try {
        const tokenData = JSON.parse(atob(this.accessToken.split('.')[1]));
        this.tokenDataJson = JSON.stringify(tokenData, null, 2);
      } catch (error) {
        this.tokenDataJson = 'Error decoding token: ' + error;
      }
    } else {
      this.tokenDataJson = 'No token available';
    }
  }
}

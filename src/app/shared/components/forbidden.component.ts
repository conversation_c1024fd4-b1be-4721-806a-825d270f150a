// src/app/shared/components/forbidden.component.ts
import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-forbidden',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    RouterModule,
  ],
  template: `
    <div class="forbidden-container">
      <div class="forbidden-content">
        <mat-card class="forbidden-card">
          <div class="forbidden-header">
            <div class="forbidden-icon">
              <mat-icon>block</mat-icon>
            </div>
            <h1 class="forbidden-title">Access Denied</h1>
          </div>

          <mat-divider></mat-divider>

          <mat-card-content>
            <p class="forbidden-message">
              You don't have permission to access this page.
            </p>
            <p class="forbidden-submessage">
              Please contact your administrator if you believe this is an error.
            </p>
          </mat-card-content>

          <mat-card-actions align="end">
            <button mat-button color="primary" (click)="goBack()">
              <mat-icon>arrow_back</mat-icon>
              Go Back
            </button>
            <button mat-raised-button color="primary" (click)="goToDashboard()">
              <mat-icon>dashboard</mat-icon>
              Go to Dashboard
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>
  `,
  styles: [
    `
      .forbidden-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 64px);
        padding: 24px;
        background-color: #f5f7fa;
      }

      .forbidden-content {
        width: 100%;
        max-width: 500px;
      }

      .forbidden-card {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .forbidden-header {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 32px 16px 24px;
        background-color: #f5f5f5;
      }

      .forbidden-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: #f44336;
        margin-bottom: 16px;
      }

      .forbidden-icon mat-icon {
        font-size: 40px;
        width: 40px;
        height: 40px;
        color: white;
      }

      .forbidden-title {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
        color: #f44336;
      }

      mat-card-content {
        padding: 24px 16px;
        text-align: center;
      }

      .forbidden-message {
        font-size: 18px;
        margin: 0 0 16px 0;
        color: #333;
      }

      .forbidden-submessage {
        font-size: 14px;
        margin: 0;
        color: #666;
      }

      mat-card-actions {
        padding: 8px 16px 16px !important;
      }

      button mat-icon {
        margin-right: 8px;
      }
    `,
  ],
})
export class ForbiddenComponent {
  constructor(private router: Router) {}

  goBack(): void {
    window.history.back();
  }

  goToDashboard(): void {
    this.router.navigate(['/admin/dashboard']);
  }
}



// Card container mixin
@mixin card-container {
  padding: $spacing-lg;
  max-width: 1200px;
  margin: 0 auto;
}

// Card styling mixin
@mixin card-styling {
  border-radius: $card-border-radius;
  box-shadow: $shadow-sm;
  overflow: hidden;
  margin-bottom: $spacing-lg;
}

// Message box mixin
@mixin message-box($color) {
  display: flex;
  align-items: center;
  padding: $spacing-md;
  margin: $spacing-md 0;
  border-radius: $border-radius-sm;
  background-color: rgba($color, 0.1);
  color: $color;

  mat-icon {
    margin-right: $spacing-sm;
  }
}

// Loading spinner mixin
@mixin loading-spinner {
  display: flex;
  justify-content: center;
  margin: $spacing-xl 0;
}

// Error message mixin
@mixin error-message {
  background-color: $error-bg;
  color: $error-color;
  padding: $spacing-md;
  border-radius: 4px;
  margin-bottom: $spacing-lg;
  display: flex;
  align-items: flex-start;
  gap: $spacing-sm;

  mat-icon {
    color: $error-color;
  }

  p {
    margin-top: $spacing-sm;
    margin-bottom: $spacing-xs;
  }

  ul {
    margin-top: 0;
  }

  .error-actions {
    display: flex;
    gap: $spacing-sm;
    margin-top: $spacing-sm;
  }
}

// Success message mixin
@mixin success-message {
  background-color: $success-bg;
  color: $success-color;
  padding: $spacing-md;
  border-radius: 4px;
  margin-bottom: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  mat-icon {
    color: $success-color;
  }
}

// No data message mixin
@mixin no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl 0;
  color: $text-lighter-color;
  text-align: center;

  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: $spacing-md;
    color: lighten($text-lighter-color, 15%);
  }

  p {
    font-size: 16px;
    margin: 0;
  }
}

// Detail item mixin
@mixin detail-item {
  margin-bottom: $spacing-md;
  display: flex;

  .label {
    font-weight: 500;
    min-width: 120px;
    color: $text-light-color;
  }

  .value {
    flex: 1;
  }

  @media (max-width: $breakpoint-sm) {
    flex-direction: column;

    .label {
      min-width: auto;
      margin-bottom: $spacing-xs;
    }
  }
}

// Table container mixin
@mixin table-container {
  width: 100%;
  overflow: auto;

  table {
    width: 100%;

    th {
      font-weight: 500;
      color: $text-color;
    }

    td {
      color: $text-secondary;
    }

    th, td {
      padding: $spacing-sm $spacing-md;
    }

    tr:hover {
      background-color: $hover-background;
    }
  }
}

// Button group mixin
@mixin button-group {
  display: flex;
  gap: $spacing-sm;

  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    width: 100%;

    button {
      width: 100%;
    }
  }
}

// Form field mixin
@mixin form-field {
  width: 100%;
  margin-bottom: $spacing-md;
}

// Flex center mixin
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Truncate text mixin
@mixin truncate-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Responsive font size mixin
@mixin responsive-font-size($min-size, $max-size, $min-width: $breakpoint-xs, $max-width: $breakpoint-lg) {
  font-size: $min-size;

  @media (min-width: $min-width) {
    font-size: calc(#{$min-size} + #{strip-unit($max-size - $min-size)} * ((100vw - #{$min-width}) / #{strip-unit($max-width - $min-width)}));
  }

  @media (min-width: $max-width) {
    font-size: $max-size;
  }
}

// Helper function to strip units
@function strip-unit($number) {
  @if type-of($number) == 'number' and not unitless($number) {
    @return $number / ($number * 0 + 1);
  }
  @return $number;
}

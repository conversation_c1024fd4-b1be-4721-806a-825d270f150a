// src/app/core/guards/permission.guard.ts
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root',
})
export class PermissionGuard implements CanActivate {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot
  ): Observable<boolean> {
    console.log('Permission Guard running for:', route.routeConfig?.path);
    console.log('Checking permission:', route.data);

    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      console.log('User is not authenticated, redirecting to login');
      this.router.navigate(['/login']);
      return of(false);
    }

    const { module, action } = route.data as { module: string; action: string };

    if (!module || !action) {
      console.error('Route is missing module or action in its data');
      return of(false);
    }

    // Get current user and log details for debugging
    const currentUser = this.authService.currentUserValue;
    console.log('Current user:', currentUser);

    // Check user roles
    const userRoles = currentUser?.roles || [];
    console.log('User roles:', userRoles);

    // Check if user has admin role (case-insensitive)
    const isAdmin = userRoles.some((role: any) => {
      if (typeof role === 'string') {
        return role.toLowerCase() === 'admin';
      } else if (role && typeof role === 'object' && role.name) {
        return role.name.toLowerCase() === 'admin';
      }
      return false;
    });
    console.log('Is admin role?', isAdmin);

    // Admin role check - gives all permissions
    if (isAdmin) {
      console.log('User has admin role - bypassing permission check');
      return of(true);
    }

    // Direct check with hasRole method
    if (this.authService.hasRole('admin')) {
      console.log(
        'hasRole method confirms admin role - bypassing permission check'
      );
      return of(true);
    }

    console.log(`Checking permission for ${module}:${action}`);
    return this.authService.hasPermission(module, action).pipe(
      tap((result) => console.log(`Permission check result: ${result}`)),
      map((hasPermission) => {
        if (!hasPermission) {
          console.error(`Permission denied: ${module} - ${action}`);
          this.router.navigate(['/forbidden']);
          return false;
        }
        console.log(`Permission granted: ${module} - ${action}`);
        return true;
      }),
      catchError((error) => {
        console.error('Permission check error:', error);
        this.router.navigate(['/login']);
        return of(false);
      })
    );
  }
}

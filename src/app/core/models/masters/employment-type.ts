export interface EmploymentType {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_by?: number;
  updated_by?: number;
  created_by_username?: string;
  updated_by_username?: string;
  created_at?: string;
  updated_at?: string;
}

export interface EmploymentTypeCreateRequest {
  name: string;
  description?: string;
  is_active?: boolean;
}

export interface EmploymentTypeUpdateRequest {
  id: number;
  name?: string;
  description?: string;
  is_active?: boolean;
}

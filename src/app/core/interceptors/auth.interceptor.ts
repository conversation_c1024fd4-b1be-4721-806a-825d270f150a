// src/app/core/interceptors/auth.interceptor.ts
import { Injectable, inject } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse,
  HttpInterceptorFn,
} from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, switchMap, filter, take, finalize } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(
    null
  );

  constructor(private authService: AuthService, private router: Router) {}

  intercept(
    request: HttpRequest<unknown>,
    next: HttpHandler
  ): Observable<HttpEvent<unknown>> {
    // Exclude auth endpoints from token addition (like login, refresh-token)
    if (this.isAuthRequest(request)) {
      return next.handle(request);
    }

    const token = this.authService.getToken();

    if (token) {
      request = this.addToken(request, token);
    }

    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        // Add detailed logs for debugging
        console.log(
          `Error intercepted: ${error.status} on ${request.url}`,
          error
        );

        if (error.status === 401) {
          // Try to refresh token on 401 errors
          return this.handle401Error(request, next);
        } else if (error.status === 403) {
          // Permission denied
          console.error(
            'Permission denied:',
            error.error?.message || 'Access forbidden'
          );
          // Fixed comparison - was causing TypeScript error
          if (request.headers.get('X-Requested-With') !== 'XMLHttpRequest') {
            this.router.navigate(['/forbidden']);
          }
        }
        return throwError(() => error);
      })
    );
  }

  private addToken(request: HttpRequest<any>, token: string): HttpRequest<any> {
    return request.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  private isAuthRequest(request: HttpRequest<any>): boolean {
    const url = request.url.toLowerCase();
    return url.includes('/login') || url.includes('/refresh-token');
  }

  private handle401Error(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    // If auth request fails with 401, just logout (avoid infinite loops)
    if (this.isAuthRequest(request)) {
      console.log('Auth request failed with 401, logging out');
      this.authService.logout();
      this.router.navigate(['/login']);
      return throwError(() => new Error('Authentication failed'));
    }

    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      console.log('Attempting to refresh token...');

      return this.authService.refreshToken().pipe(
        switchMap((response) => {
          console.log('Token refresh successful', response);
          this.isRefreshing = false;

          // Make sure we have a token before proceeding
          if (!response || !response.data || !response.data.accessToken) {
            throw new Error('Invalid token refresh response');
          }

          const newToken = response.data.accessToken;
          this.refreshTokenSubject.next(newToken);

          return next.handle(this.addToken(request, newToken));
        }),
        catchError((refreshError) => {
          console.error('Token refresh failed:', refreshError);
          this.isRefreshing = false;
          this.refreshTokenSubject.next(null);

          // Only logout and redirect for critical auth failures
          this.authService.logout();

          // Show error notification before redirect
          setTimeout(() => {
            this.router.navigate(['/login'], {
              queryParams: {
                returnUrl: this.router.url,
                reason: 'session_expired',
              },
            });
          }, 1000); // Small delay to allow any component error handlers to show messages first

          return throwError(() => refreshError);
        }),
        finalize(() => {
          this.isRefreshing = false;
        })
      );
    } else {
      // Wait for token to be refreshed
      return this.refreshTokenSubject.pipe(
        filter((token) => token !== null),
        take(1),
        switchMap((token) => {
          return next.handle(this.addToken(request, token));
        })
      );
    }
  }
}

// Functional interceptor for Angular 18+
export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  // Don't add token to auth requests
  if (req.url.includes('/login') || req.url.includes('/refresh-token')) {
    return next(req);
  }

  const token = authService.getToken();

  if (token) {
    req = req.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      console.log(`Error in functional interceptor: ${error.status}`, error);

      if (error.status === 401) {
        // For 401 errors, try to refresh the token
        console.log('Trying to refresh token in functional interceptor');

        return authService.refreshToken().pipe(
          switchMap((response) => {
            if (!response || !response.data || !response.data.accessToken) {
              throw new Error('Invalid token refresh response');
            }

            const newToken = response.data.accessToken;
            const newRequest = req.clone({
              setHeaders: {
                Authorization: `Bearer ${newToken}`,
              },
            });
            return next(newRequest);
          }),
          catchError((refreshError) => {
            console.error(
              'Token refresh failed in functional interceptor:',
              refreshError
            );
            authService.logout();

            // Small delay to allow component error handlers to show messages
            setTimeout(() => {
              router.navigate(['/login'], {
                queryParams: {
                  returnUrl: router.url,
                  reason: 'session_expired',
                },
              });
            }, 1000);

            return throwError(() => refreshError);
          })
        );
      } else if (error.status === 403) {
        // Permission denied
        console.error(
          'Permission denied:',
          error.error?.message || 'Access forbidden'
        );
        router.navigate(['/forbidden']);
      }
      return throwError(() => error);
    })
  );
};

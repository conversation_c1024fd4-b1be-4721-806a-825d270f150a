// src/app/core/services/auth.service.ts

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError, of } from 'rxjs';
import { catchError, map, tap, shareReplay } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { Router } from '@angular/router';
import { TokenService } from './token.service';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private apiUrl = environment.apiUrl;
  private currentUserSubject: BehaviorSubject<any>;
  public currentUser: Observable<any>;

  // Permission cache to avoid too many API calls
  private permissionCache: Map<string, Observable<boolean>> = new Map();
  // Sync permission cache for immediate UI decisions
  private syncPermissionCache: Map<string, boolean> = new Map();

  constructor(
    private http: HttpClient,
    private router: Router,
    private tokenService: TokenService
  ) {
    this.currentUserSubject = new BehaviorSubject<any>(
      JSON.parse(localStorage.getItem('currentUser') || 'null')
    );
    this.currentUser = this.currentUserSubject.asObservable();
  }

  public get currentUserValue(): any {
    return this.currentUserSubject.value;
  }

  login(identifier: string, password: string): Observable<any> {
    // Clear permission cache on login
    this.clearPermissionCache();

    return this.http
      .post<any>(`${this.apiUrl}/auth/login`, { identifier, password })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            // Store user and token details in local storage
            localStorage.setItem(
              'currentUser',
              JSON.stringify(response.data.user)
            );
            // Use TokenService to store tokens
            this.tokenService.setTokens(
              response.data.accessToken,
              response.data.refreshToken
            );

            // Update the current user subject
            this.currentUserSubject.next(response.data.user);

            // Prefetch permissions after successful login
            setTimeout(() => {
              this.clearPermissionCache(); // Clear cache first to ensure fresh data
              this.prefetchCommonPermissions();
            }, 500);
          }
          return response;
        }),
        catchError((error) => {
          return throwError(() => error);
        })
      );
  }

  register(userData: any): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/auth/register`, userData).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  logout(): void {
    // Clear permission cache on logout
    this.clearPermissionCache();

    // Remove user data from local storage
    localStorage.removeItem('currentUser');
    // Use TokenService to clear tokens
    this.tokenService.clearTokens();

    // Reset the current user subject
    this.currentUserSubject.next(null);

    // Navigate to login page
    this.router.navigate(['/login']);
  }

  refreshToken(): Observable<any> {
    const refreshToken = this.tokenService.getRefreshToken();

    if (!refreshToken) {
      return throwError(() => new Error('No refresh token available'));
    }

    return this.http
      .post<any>(`${this.apiUrl}/auth/refresh-token`, { refreshToken })
      .pipe(
        tap((response) => {
          if (response.success && response.data) {
            // Update tokens using TokenService
            this.tokenService.setTokens(
              response.data.accessToken,
              response.data.refreshToken
            );

            // Update user data if it's changed
            if (response.data.user) {
              localStorage.setItem(
                'currentUser',
                JSON.stringify(response.data.user)
              );
              this.currentUserSubject.next(response.data.user);

              // Prefetch permissions after token refresh
              this.prefetchCommonPermissions();
            }
          }
        }),
        catchError((error) => {
          // If refresh fails, log the user out
          this.logout();
          return throwError(() => error);
        })
      );
  }

  isAuthenticated(): boolean {
    const currentUser = this.currentUserValue;
    const token = this.tokenService.getAccessToken();
    return !!currentUser && !!token;
  }

  hasPermission(module: string, permission: string): Observable<boolean> {
    // Check if user is authenticated
    if (!this.isAuthenticated()) {
      return of(false);
    }

    // Create cache key
    const cacheKey = `${module}:${permission}`;

    // Check if permission is cached
    if (this.permissionCache.has(cacheKey)) {
      return this.permissionCache.get(cacheKey)!;
    }

    // Make API request
    const permissionCheck = this.http
      .post<any>(`${this.apiUrl}/auth/check-permission`, {
        module,
        permission,
      })
      .pipe(
        map((response) => !!response.hasPermission),
        tap((result) => {
          // Also cache in sync cache for immediate access
          this.syncPermissionCache.set(cacheKey, result);
        }),
        catchError((error) => {
          // Cache false result in sync cache
          this.syncPermissionCache.set(cacheKey, false);
          return of(false);
        }),
        shareReplay(1) // Cache the result
      );

    // Store in cache
    this.permissionCache.set(cacheKey, permissionCheck);

    return permissionCheck;
  }

  // Clear permission cache (useful when roles/permissions change)
  clearPermissionCache(): void {
    this.permissionCache.clear();
    this.syncPermissionCache.clear();
  }

  // Synchronous permission check for UI decisions
  // This is approximate and should not be used for security-critical decisions
  hasPermissionSync(module: string, permission: string): boolean {
    // Check if user is authenticated
    if (!this.isAuthenticated()) {
      return false;
    }

    // Admin role check - gives all permissions
    if (this.hasRole('admin')) {
      return true;
    }

    // Create cache key
    const cacheKey = `${module}:${permission}`;

    // Check sync cache first
    if (this.syncPermissionCache.has(cacheKey)) {
      return this.syncPermissionCache.get(cacheKey)!;
    }

    // If not in sync cache, trigger async check and cache result
    this.hasPermission(module, permission).subscribe({
      next: (result) => {
        this.syncPermissionCache.set(cacheKey, result);
      },
      error: () => {
        this.syncPermissionCache.set(cacheKey, false);
      },
    });

    // Return false for first check, will be updated on next check
    return false;
  }

  hasRole(role: string): boolean {
    const currentUser = this.currentUserValue;

    if (!currentUser || !currentUser.roles) {
      return false;
    }

    // Handle array of roles
    if (Array.isArray(currentUser.roles)) {
      const hasRole = currentUser.roles.some((userRole: any) => {
        // Handle string roles
        if (typeof userRole === 'string') {
          return userRole.toLowerCase() === role.toLowerCase();
        }
        // Handle object roles with name property
        else if (userRole && typeof userRole === 'object' && userRole.name) {
          return userRole.name.toLowerCase() === role.toLowerCase();
        }
        return false;
      });

      return hasRole;
    }
    // Handle string role
    else if (typeof currentUser.roles === 'string') {
      const hasRole = currentUser.roles.toLowerCase() === role.toLowerCase();
      return hasRole;
    }

    return false;
  }

  getToken(): string | null {
    return this.tokenService.getAccessToken();
  }

  // Prefetch common permissions to improve UI responsiveness
  prefetchCommonPermissions(): void {
    if (!this.isAuthenticated()) {
      return;
    }

    // List of common module-permission pairs to prefetch
    const commonPermissions = [
      { module: 'dashboard', permission: 'read' },
      { module: 'user', permission: 'read' },
      { module: 'user', permission: 'create' },
      { module: 'user', permission: 'update' },
      { module: 'user', permission: 'delete' },
      { module: 'role', permission: 'read' },
      { module: 'role', permission: 'create' },
      { module: 'role', permission: 'update' },
      { module: 'role', permission: 'delete' },
      { module: 'module', permission: 'read' },
      { module: 'module', permission: 'create' },
      { module: 'module', permission: 'update' },
      { module: 'module', permission: 'delete' },
      { module: 'permission', permission: 'read' },
      { module: 'permission', permission: 'update' },
      { module: 'masters', permission: 'read' },
      { module: 'masters', permission: 'create' },
      { module: 'masters', permission: 'update' },
      { module: 'masters', permission: 'delete' },
      { module: 'category', permission: 'read' },
      { module: 'product', permission: 'read' },
      { module: 'customer', permission: 'read' },
      { module: 'settings', permission: 'read' },
    ];

    // Prefetch each permission
    for (const { module, permission } of commonPermissions) {
      this.hasPermission(module, permission).subscribe({
        next: (result) => {
          // Permission cached silently
        },
        error: (error) => {
          // Error handled silently
        },
      });
    }
  }
}

// src/app/core/services/module.service.ts

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Module } from '../models/module';
import { environment } from '../../../environments/environment';
import { tap, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ModuleService {
  private apiUrl = `${environment.apiUrl}/modules`;

  constructor(private http: HttpClient) {}

  // Get all modules
  getModules(
    includeInactive = false
  ): Observable<{ success: boolean; data: Module[] }> {
    const url = `${this.apiUrl}?includeInactive=${includeInactive}`;
    console.log('Calling module API:', url);

    return this.http.get<{ success: boolean; data: Module[] }>(url).pipe(
      tap((response) => console.log('API response:', response)),
      catchError((error) => {
        console.error('API error:', error);
        return throwError(() => error);
      })
    );
  }

  // Get module by ID
  getModuleById(id: number): Observable<{ success: boolean; data: Module }> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Fetching module by ID:', url);

    return this.http.get<{ success: boolean; data: Module }>(url).pipe(
      tap((response) => console.log('Module by ID response:', response)),
      catchError((error) => {
        console.error('Error fetching module:', error);
        // Include more detailed error information
        const errorMsg =
          error.error?.message ||
          error.statusText ||
          error.message ||
          'Unknown error';
        return throwError(
          () =>
            new Error(
              `Failed to load module: ${errorMsg} (Status: ${error.status})`
            )
        );
      })
    );
  }

  // Create new module
  createModule(
    module: Partial<Module>
  ): Observable<{ success: boolean; message: string; data: Module }> {
    return this.http
      .post<{ success: boolean; message: string; data: Module }>(
        this.apiUrl,
        module
      )
      .pipe(
        catchError((error) => {
          console.error('Error creating module:', error);
          return throwError(() => error);
        })
      );
  }

  // Update module
  updateModule(
    id: number,
    module: Partial<Module>
  ): Observable<{ success: boolean; message: string; data: Module }> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Updating module:', url, module);

    return this.http
      .put<{ success: boolean; message: string; data: Module }>(url, module)
      .pipe(
        tap((response) => console.log('Module update response:', response)),
        catchError((error) => {
          console.error('Error updating module:', error);
          // Include more detailed error information
          const errorMsg =
            error.error?.message ||
            error.statusText ||
            error.message ||
            'Unknown error';
          return throwError(
            () =>
              new Error(
                `Failed to update module: ${errorMsg} (Status: ${error.status})`
              )
          );
        })
      );
  }

  // Delete module
  deleteModule(id: number): Observable<{ success: boolean; message: string }> {
    return this.http
      .delete<{ success: boolean; message: string }>(`${this.apiUrl}/${id}`)
      .pipe(
        catchError((error) => {
          console.error('Error deleting module:', error);
          return throwError(() => error);
        })
      );
  }

  // Toggle module status
  toggleModuleStatus(
    id: number,
    isActive: boolean
  ): Observable<{ success: boolean; message: string; data: Module }> {
    return this.http
      .patch<{ success: boolean; message: string; data: Module }>(
        `${this.apiUrl}/${id}/status`,
        { is_active: isActive }
      )
      .pipe(
        catchError((error) => {
          console.error('Error toggling module status:', error);
          return throwError(() => error);
        })
      );
  }

  // Bulk delete modules
  bulkDeleteModules(
    ids: number[]
  ): Observable<{ success: boolean; message: string; data?: any }> {
    console.log('Bulk deleting modules with IDs:', ids);
    return this.http
      .post<{ success: boolean; message: string; data?: any }>(
        `${this.apiUrl}/bulk-delete`,
        { ids }
      )
      .pipe(
        tap((response) => console.log('Bulk delete response:', response)),
        catchError((error) => {
          console.error('Error bulk deleting modules:', error);
          return throwError(() => error);
        })
      );
  }
}

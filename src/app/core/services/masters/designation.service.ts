import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import {
  Designation,
  DesignationResponse,
} from '../../models/masters/designation';

@Injectable({
  providedIn: 'root',
})
export class DesignationService {
  private apiUrl = `${environment.apiUrl}/masters/designations`;

  constructor(private http: HttpClient) {}

  // Get all designations
  getDesignations(includeInactive = false): Observable<DesignationResponse> {
    const url = `${this.apiUrl}?includeInactive=${includeInactive}`;

    return this.http.get<DesignationResponse>(url).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Get designation by ID
  getDesignationById(id: number): Observable<DesignationResponse> {
    const url = `${this.apiUrl}/${id}`;

    return this.http.get<DesignationResponse>(url).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Create new designation
  createDesignation(
    designation: Partial<Designation>
  ): Observable<DesignationResponse> {
    return this.http.post<DesignationResponse>(this.apiUrl, designation).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Update designation
  updateDesignation(
    id: number,
    designation: Partial<Designation>
  ): Observable<DesignationResponse> {
    const url = `${this.apiUrl}/${id}`;

    return this.http.put<DesignationResponse>(url, designation).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Delete designation
  deleteDesignation(id: number): Observable<DesignationResponse> {
    const url = `${this.apiUrl}/${id}`;

    return this.http.delete<DesignationResponse>(url).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Toggle designation status
  toggleDesignationStatus(
    id: number,
    isActive: boolean
  ): Observable<DesignationResponse> {
    const url = `${this.apiUrl}/${id}/status`;

    return this.http
      .patch<DesignationResponse>(url, { is_active: isActive })
      .pipe(
        catchError((error) => {
          return throwError(() => error);
        })
      );
  }

  // Bulk delete designations
  bulkDeleteDesignations(ids: number[]): Observable<DesignationResponse> {
    const url = `${this.apiUrl}/bulk-delete`;

    return this.http.post<DesignationResponse>(url, { ids }).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }
}

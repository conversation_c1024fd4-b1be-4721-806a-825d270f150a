import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, catchError, map, throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface ProjectType {
  id?: number;
  name: string;
  is_active: boolean;
  created_by?: number;
  updated_by?: number;
  created_by_username?: string;
  updated_by_username?: string;
  created_at?: string;
  updated_at?: string;
}

// Response interfaces for API communication
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: any;
}

@Injectable({
  providedIn: 'root',
})
export class ProjectTypeService {
  private apiUrl = `${environment.apiUrl}/masters/project-types`;

  constructor(private http: HttpClient) {}

  /**
   * Get all project types
   */
  getAllProjectTypes(
    includeInactive: boolean = false
  ): Observable<ProjectType[]> {
    let params = new HttpParams().set(
      'includeInactive',
      includeInactive.toString()
    );

    return this.http
      .get<ApiResponse<ProjectType[]>>(this.apiUrl, { params })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          return [];
        }),
        catchError((error) => {
          console.error('Error fetching project types:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Get project type by ID
   */
  getProjectTypeById(id: number): Observable<ProjectType> {
    return this.http.get<ApiResponse<ProjectType>>(`${this.apiUrl}/${id}`).pipe(
      map((response) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Project type not found');
      }),
      catchError((error) => {
        console.error(`Error fetching project type with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Create a new project type
   */
  createProjectType(projectType: ProjectType): Observable<ProjectType> {
    return this.http
      .post<ApiResponse<ProjectType>>(this.apiUrl, projectType)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.message || 'Failed to create project type');
        }),
        catchError((error) => {
          console.error('Error creating project type:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Update an existing project type
   */
  updateProjectType(
    id: number,
    projectType: ProjectType
  ): Observable<ProjectType> {
    return this.http
      .put<ApiResponse<ProjectType>>(`${this.apiUrl}/${id}`, projectType)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.message || 'Failed to update project type');
        }),
        catchError((error) => {
          console.error(`Error updating project type with ID ${id}:`, error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Delete a project type
   */
  deleteProjectType(id: number): Observable<boolean> {
    return this.http.delete<ApiResponse<any>>(`${this.apiUrl}/${id}`).pipe(
      map((response) => {
        return response.success;
      }),
      catchError((error) => {
        console.error(`Error deleting project type with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Toggle project type status (active/inactive)
   */
  toggleProjectTypeStatus(
    id: number,
    isActive: boolean
  ): Observable<ProjectType> {
    return this.http
      .patch<ApiResponse<ProjectType>>(`${this.apiUrl}/${id}/status`, {
        is_active: isActive,
      })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(
            response.message || 'Failed to update project type status'
          );
        }),
        catchError((error) => {
          console.error(
            `Error toggling status for project type with ID ${id}:`,
            error
          );
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Bulk delete project types
   */
  bulkDeleteProjectTypes(ids: number[]): Observable<boolean> {
    return this.http
      .post<ApiResponse<any>>(`${this.apiUrl}/bulk-delete`, { ids })
      .pipe(
        map((response) => {
          return response.success;
        }),
        catchError((error) => {
          console.error('Error bulk deleting project types:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Helper method to extract error messages
   */
  private getErrorMessage(error: any): string {
    if (error.error && error.error.message) {
      return error.error.message;
    }
    return error.message || 'An unknown error occurred';
  }
}

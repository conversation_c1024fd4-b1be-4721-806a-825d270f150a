import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, catchError, map, throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';
import {
  Qualification,
  QualificationCreateRequest,
  QualificationUpdateRequest,
} from '../../models/masters/qualification';

// Response interfaces for API communication
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: any;
}

@Injectable({
  providedIn: 'root',
})
export class QualificationService {
  private apiUrl = `${environment.apiUrl}/masters/qualifications`;

  constructor(private http: HttpClient) {}

  /**
   * Get all qualifications
   */
  getAllQualifications(
    includeInactive: boolean = false
  ): Observable<Qualification[]> {
    let params = new HttpParams().set(
      'includeInactive',
      includeInactive.toString()
    );

    return this.http
      .get<ApiResponse<Qualification[]>>(this.apiUrl, { params })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          return [];
        }),
        catchError((error) => {
          console.error('Error fetching qualifications:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Get qualification by ID
   */
  getQualificationById(id: number): Observable<Qualification> {
    return this.http
      .get<ApiResponse<Qualification>>(`${this.apiUrl}/${id}`)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error('Qualification not found');
        }),
        catchError((error) => {
          console.error(`Error fetching qualification with ID ${id}:`, error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Create a new qualification
   */
  createQualification(
    qualification: QualificationCreateRequest
  ): Observable<Qualification> {
    return this.http
      .post<ApiResponse<Qualification>>(this.apiUrl, qualification)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.message || 'Failed to create qualification');
        }),
        catchError((error) => {
          console.error('Error creating qualification:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Update an existing qualification
   */
  updateQualification(
    id: number,
    qualification: QualificationUpdateRequest
  ): Observable<Qualification> {
    return this.http
      .put<ApiResponse<Qualification>>(`${this.apiUrl}/${id}`, qualification)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.message || 'Failed to update qualification');
        }),
        catchError((error) => {
          console.error(`Error updating qualification with ID ${id}:`, error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Delete a qualification
   */
  deleteQualification(id: number): Observable<boolean> {
    return this.http.delete<ApiResponse<any>>(`${this.apiUrl}/${id}`).pipe(
      map((response) => {
        return response.success;
      }),
      catchError((error) => {
        console.error(`Error deleting qualification with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Toggle qualification status (active/inactive)
   */
  toggleQualificationStatus(
    id: number,
    isActive: boolean
  ): Observable<Qualification> {
    return this.http
      .patch<ApiResponse<Qualification>>(`${this.apiUrl}/${id}/status`, {
        is_active: isActive,
      })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(
            response.message || 'Failed to update qualification status'
          );
        }),
        catchError((error) => {
          console.error(
            `Error toggling status for qualification with ID ${id}:`,
            error
          );
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Bulk delete qualifications
   */
  bulkDeleteQualifications(ids: number[]): Observable<boolean> {
    return this.http
      .post<ApiResponse<any>>(`${this.apiUrl}/bulk-delete`, { ids })
      .pipe(
        map((response) => {
          return response.success;
        }),
        catchError((error) => {
          console.error('Error bulk deleting qualifications:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Helper method to extract error messages
   */
  private getErrorMessage(error: any): string {
    if (error.error && error.error.message) {
      return error.error.message;
    }
    return error.message || 'An unknown error occurred';
  }
}

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import {
  Department,
  DepartmentResponse,
} from '../../models/masters/department';

@Injectable({
  providedIn: 'root',
})
export class DepartmentService {
  private apiUrl = `${environment.apiUrl}/masters/departments`;

  constructor(private http: HttpClient) {}

  // Get all departments
  getDepartments(includeInactive = false): Observable<DepartmentResponse> {
    const url = `${this.apiUrl}?includeInactive=${includeInactive}`;

    return this.http.get<DepartmentResponse>(url).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Get department by ID
  getDepartmentById(id: number): Observable<DepartmentResponse> {
    const url = `${this.apiUrl}/${id}`;

    return this.http.get<DepartmentResponse>(url).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Create new department
  createDepartment(
    department: Partial<Department>
  ): Observable<DepartmentResponse> {
    return this.http.post<DepartmentResponse>(this.apiUrl, department).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Update department
  updateDepartment(
    id: number,
    department: Partial<Department>
  ): Observable<DepartmentResponse> {
    const url = `${this.apiUrl}/${id}`;

    return this.http.put<DepartmentResponse>(url, department).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Delete department
  deleteDepartment(id: number): Observable<DepartmentResponse> {
    const url = `${this.apiUrl}/${id}`;

    return this.http.delete<DepartmentResponse>(url).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Toggle department status
  toggleDepartmentStatus(
    id: number,
    isActive: boolean
  ): Observable<DepartmentResponse> {
    const url = `${this.apiUrl}/${id}/status`;

    return this.http
      .patch<DepartmentResponse>(url, { is_active: isActive })
      .pipe(
        catchError((error) => {
          return throwError(() => error);
        })
      );
  }

  // Bulk delete departments
  bulkDeleteDepartments(ids: number[]): Observable<DepartmentResponse> {
    const url = `${this.apiUrl}/bulk-delete`;

    return this.http.post<DepartmentResponse>(url, { ids }).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }
}

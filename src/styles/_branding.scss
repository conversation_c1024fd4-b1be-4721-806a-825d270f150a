// Branding-specific styles and utilities
// This file provides additional styles that work with the dynamic branding system

// CSS Custom Properties for Branding
// These are set by the BrandingService and can be used directly in components
:root {
  // Default branding values - will be overridden by BrandingService
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --accent-color: #4CAF50;
  --error-color: #FF5722;
  --background-color: #F8FAFC;
  --primary-contrast: #ffffff;
  --secondary-contrast: #ffffff;
}

// Branding utility classes
.branded {
  // Primary color utilities
  &-primary {
    color: var(--primary-color) !important;
  }
  
  &-primary-bg {
    background-color: var(--primary-color) !important;
    color: var(--primary-contrast) !important;
  }

  // Secondary color utilities
  &-secondary {
    color: var(--secondary-color) !important;
  }
  
  &-secondary-bg {
    background-color: var(--secondary-color) !important;
    color: var(--secondary-contrast) !important;
  }

  // Accent color utilities
  &-accent {
    color: var(--accent-color) !important;
  }
  
  &-accent-bg {
    background-color: var(--accent-color) !important;
    color: white !important;
  }

  // Error color utilities
  &-error {
    color: var(--error-color) !important;
  }
  
  &-error-bg {
    background-color: var(--error-color) !important;
    color: white !important;
  }

  // Background utilities
  &-background {
    background-color: var(--background-color) !important;
  }
}

// Branded button styles
.btn-branded {
  &-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--primary-contrast);

    &:hover {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      opacity: 0.9;
    }
  }

  &-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--secondary-contrast);

    &:hover {
      background-color: var(--secondary-color);
      border-color: var(--secondary-color);
      opacity: 0.9;
    }
  }

  &-accent {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;

    &:hover {
      background-color: var(--accent-color);
      border-color: var(--accent-color);
      opacity: 0.9;
    }
  }
}

// Branded card styles
.card-branded {
  border-left: 4px solid var(--primary-color);
  
  .card-header {
    background-color: rgba(var(--primary-color-rgb, 102, 126, 234), 0.1);
    border-bottom: 1px solid rgba(var(--primary-color-rgb, 102, 126, 234), 0.2);
  }
}

// Branded gradient backgrounds
.gradient-branded {
  &-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  }
  
  &-subtle {
    background: linear-gradient(135deg, var(--background-color) 0%, rgba(var(--primary-color-rgb, 102, 126, 234), 0.1) 100%);
  }
}

// Branded text colors with opacity variations
.text-branded {
  &-primary {
    color: var(--primary-color);
    
    &-light {
      color: rgba(var(--primary-color-rgb, 102, 126, 234), 0.7);
    }
    
    &-lighter {
      color: rgba(var(--primary-color-rgb, 102, 126, 234), 0.5);
    }
  }
  
  &-secondary {
    color: var(--secondary-color);
    
    &-light {
      color: rgba(var(--secondary-color-rgb, 118, 75, 162), 0.7);
    }
  }
}

// Branded border utilities
.border-branded {
  &-primary {
    border-color: var(--primary-color) !important;
  }
  
  &-secondary {
    border-color: var(--secondary-color) !important;
  }
  
  &-accent {
    border-color: var(--accent-color) !important;
  }
  
  &-left {
    border-left: 3px solid var(--primary-color);
  }
  
  &-top {
    border-top: 3px solid var(--primary-color);
  }
}

// Only apply branded focus to specific elements when requested
.branded-focus:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb, 102, 126, 234), 0.25) !important;
}

// Responsive branding utilities
@media (max-width: 768px) {
  .branded-primary-bg,
  .branded-secondary-bg {
    padding: 0.5rem;
  }
}

// Print styles - use neutral colors for printing
@media print {
  .branded-primary,
  .branded-secondary,
  .branded-accent {
    color: #333 !important;
  }
  
  .branded-primary-bg,
  .branded-secondary-bg,
  .branded-accent-bg {
    background-color: #f5f5f5 !important;
    color: #333 !important;
  }
}
// Colors
$primary-color: #3f51b5;
$secondary-color: #673ab7;
$accent-color: #ff4081;
$warn-color: #f44336;
$success-color: #4caf50;
$warning-color: #ff9800;
$error-color: #f44336;
$info-color: #2196f3;

// Background colors for messages
$error-bg: rgba($error-color, 0.1);
$success-bg: rgba($success-color, 0.1);
$warning-bg: rgba($warning-color, 0.1);
$info-bg: rgba($info-color, 0.1);

// Text colors
$text-color: #333333;
$text-secondary: #757575;
$text-disabled: #bdbdbd;
$text-light: #ffffff;
$text-light-color: #666666;
$text-lighter-color: #999999;

// Border colors
$border-color: #e0e0e0;

// Disabled states
$disabled-color: #f5f5f5;
$disabled-text-color: #bdbdbd;

// Background colors
$background-color: #f5f5f5;
$card-background: #ffffff;
$hover-background: rgba(0, 0, 0, 0.04);

// Table colors
$table-header-background: #f8f9fa;
$table-header-text: #333333;
$table-row-hover: rgba(0, 0, 0, 0.04);

// Spacing
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// Border radius
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 16px;
$card-border-radius: 8px;

// Shadows
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
$shadow-md: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
$shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);

// Breakpoints
$breakpoint-xs: 576px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// Sidebar variables
$sidebar-bg: #2c3e50;
$sidebar-text: #ecf0f1;
$sidebar-active: #3498db;
$sidebar-hover: rgba(255, 255, 255, 0.1);

// Status colors
$status-active-color: #2ecc71;
$status-inactive-color: #e74c3c;

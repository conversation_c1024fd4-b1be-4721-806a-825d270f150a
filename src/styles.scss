/* You can add global styles to this file, and also import other style files */
@import 'styles/variables';
@import 'styles/mixins';
@import 'styles/branding';

html, body { height: 100%; }
body {
  margin: 0;
  font-family: <PERSON>o, "Helvetica Neue", sans-serif;
  background-color: $background-color;
}

/* Snackbar styles */
.success-snackbar {
  background-color: $success-color;
  color: white;
}

.error-snackbar {
  background-color: $warn-color;
  color: white;
}

/* Permission management styles */
.permission-item {
  display: flex;
  align-items: center;
}

/* Enhance checkbox visibility */
.mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
  border-color: $primary-color !important;
  background-color: $primary-color !important;
}

.mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background .mdc-checkbox__checkmark {
  color: white !important;
}

/* Error message styling */
.error-message {
  color: $error-color;
  background-color: $error-bg;
  padding: $spacing-md;
  border-radius: 4px;
  margin: $spacing-sm 0;
  font-weight: 500;
}

/* Success message styling */
.success-message {
  color: $success-color;
  background-color: $success-bg;
  padding: $spacing-md;
  border-radius: 4px;
  margin: $spacing-sm 0;
  font-weight: 500;
}

/* Card styling enhancements */
mat-card {
  border-radius: $card-border-radius;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Button styling consistency */
button.mat-raised-button {
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Table styling consistency */
.mat-table {
  width: 100%;

  .mat-header-cell {
    font-weight: 500;
    color: $text-color;
  }

  .mat-cell {
    color: $text-color;
  }
}
